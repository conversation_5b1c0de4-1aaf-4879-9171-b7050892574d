import { useEffect, useState, useCallback, useMemo } from 'react'
import { useGlobalState } from '@/hooks/useGlobalState'
import { getAvatarFromProfile } from '@/lib/avatarUtils'
import { getUserProfile } from '@/services/userService'

// Global flag to prevent multiple profile loads
let isProfileLoading = false

export const useProfile = (isAuthReady: boolean) => {
  const { auth } = useGlobalState()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastLoadTime, setLastLoadTime] = useState<number>(0)
  const [hasLoaded, setHasLoaded] = useState(false)
  const LOAD_PROFILE_COOLDOWN = 5000 // 5 seconds cooldown

  const loadProfile = useCallback(async () => {
    if (!isAuthReady || !auth.token) return

    // Skip if already loaded and has profile data
    if (hasLoaded && auth.profile) {
      console.log('✅ Profile already loaded, skipping...')
      return
    }

    if (isProfileLoading) {
      return
    }

    // Rate limiting to prevent excessive API calls
    const now = Date.now()
    if (now - lastLoadTime < LOAD_PROFILE_COOLDOWN) {
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      setLastLoadTime(now)
      isProfileLoading = true

      const profileRes = await getUserProfile().catch(err => {
        console.warn('Profile load failed:', err)
        return null
      })

      // Update profile
      if (profileRes?.data) {
        auth.setProfile(profileRes.data)
        setHasLoaded(true)

        // Extract shop_id if not already set
        if (!auth.shop_id) {
          const shopId = (profileRes.data as any).shop?.shop_id ||
            (profileRes.data as any).shop?._id ||
            (profileRes.data as any).package?.shop_id
          if (shopId) {
            auth.setUserData({ shop_id: shopId })
          }
        }
      }

    } catch (error: any) {
      console.error('❌ Failed to load profile:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
      isProfileLoading = false
    }
  }, [isAuthReady, auth.token, auth.shop_id, auth.profile, auth.setProfile, auth.setUserData, lastLoadTime, hasLoaded, LOAD_PROFILE_COOLDOWN])

  useEffect(() => {
    loadProfile()
  }, [loadProfile])

  // Memoized profile data
  const profile = useMemo(() => auth.profile, [auth.profile])

  // Memoized profile info
  const profileInfo = useMemo(() => {
    if (!profile) return null

    // Tạo avatar URL từ profile sử dụng utility function
    const originalAvatar = profile.avatar || profile.profile_picture;
    const avatarUrl = getAvatarFromProfile(profile, originalAvatar);

    return {
      name: profile.name || profile.username || 'Unknown User',
      email: profile.email,
      avatar: avatarUrl,
      shop: profile.shop,
      package: profile.package,
      permissions: profile.permissions || []
    }
  }, [profile])

  // Profile actions
  const updateProfile = useCallback((updates: any) => {
    if (profile) {
      const updatedProfile = { ...profile, ...updates }
      auth.setProfile(updatedProfile)
    }
  }, [profile, auth])

  const clearProfile = useCallback(() => {
    auth.setProfile(null)
  }, [auth])

  return {
    profile,
    profileInfo,
    isLoading,
    error,
    // Actions
    loadProfile,
    updateProfile,
    clearProfile,
    reload: loadProfile
  }
}
