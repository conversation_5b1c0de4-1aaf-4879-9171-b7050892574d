
// Main types export file - Centralized type exports

// Export all types from individual files
export * from './api';
export * from './auth';
export * from './chat';
export * from './conversation';
export * from './profile';
export * from './user';
export * from './components';
export * from './sendChat';
export * from './shop';
export * from './photo';

// Re-export commonly used types for convenience
export type {
    DataResponseType,
    ApiError,
    PaginationParams,
    PaginatedResponse
} from './api';

export type {
    AuthState,
    AuthActions,
    UserInfo,
    DomainConfig,
    GoogleAuthConfig,
    CookieConfig,
    COOKIE_KEYS
} from './auth';

export type {
    Message,
    ChatRequest,
    ChatResponse,
    ChatPayload
} from './chat';

export type {
    Conversation,
    CreateConversationRequest
} from './conversation';

export type {
    UserProfile
} from './profile';

// NextAuth types extension
declare module "next-auth" {
    interface Session {
        accessToken?: string
        provider?: string
        user: {
            id?: string
            name?: string | null
            email?: string | null
            image?: string | null
        }
    }
}
