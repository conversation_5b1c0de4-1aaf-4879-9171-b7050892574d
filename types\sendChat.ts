import type { Message } from '@/types';

export interface SendChatProps {
  conversationId: string
  promptId: string
  shopId: string
  userId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
}

export interface SendChatComponentProps {
  conversationId: string
  promptId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
  messages?: Message[]
  setMessages?: React.Dispatch<React.SetStateAction<Message[]>>
}

export interface SendChatFormData {
  inputValue: string
}

export interface ChatPayload {
  conversation_id: string
  query: string
  prompt_id: string
  shop_id: string
  version: string
  user_id: string
  website_id: string
}