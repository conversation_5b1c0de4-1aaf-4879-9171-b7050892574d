"use client"

import React from 'react'
import Image from 'next/image'
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'

interface WebsiteInfoProps {
  className?: string
}

/**
 * Component hiển thị thông tin website từ prompt.website
 */
export const WebsiteInfo: React.FC<WebsiteInfoProps> = ({ className = "" }) => {
  const {
    websiteInfo,
    isLoading,
  } = useWebsiteInfo()

  if (isLoading) {
    return (
      <div className={`flex flex-col items-center ${className}`}>
        <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse mb-2"></div>
        <div className="w-32 h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
        <div className="w-48 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col items-center ${className}`}>
      {/* Logo/Favicon */}
      <div className="relative mb-4">
        <Image
          src={websiteInfo.logo || websiteInfo.favicon}
          alt={websiteInfo.meta_title}
          width={64}
          height={64}
          className="rounded-2xl object-contain"
          priority
          onError={(e) => {
            // Fallback nếu logo không load được
            const target = e.target as HTMLImageElement
            target.src = "/favicon.ico"
          }}
        />
      </div>

      {/* Title */}
      <h1 className="text-3xl font-bold text-center mb-2">
        {websiteInfo.meta_title}
      </h1>

      {/* Description */}
      <p className="text-sm text-gray-500 text-center mb-4 max-w-md">
        {websiteInfo.meta_description}
      </p>

      {/* Thumbnail (nếu khác với logo) */}
      {websiteInfo.thumbnail && websiteInfo.thumbnail !== websiteInfo.logo && (
        <div className="relative mb-2">
          <Image
            src={websiteInfo.thumbnail}
            alt={websiteInfo.meta_title}
            width={120}
            height={60}
            className="rounded-lg object-contain opacity-80"
            onError={(e) => {
              // Ẩn thumbnail nếu không load được
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
            }}
          />
        </div>
      )}
    </div>
  )
}
