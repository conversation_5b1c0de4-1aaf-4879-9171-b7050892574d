"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader2 } from 'lucide-react'
import React from 'react'

import { useDomain } from '@/components/providers/DomainProvider'

interface DomainGuardProps {
  children: React.ReactNode
}

export function DomainGuard({ children }: DomainGuardProps) {
  const { isVerifying, isAllowed, error, domainConfig, verificationStep } = useDomain()

  // Loading state
  if (isVerifying) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
            <Loader2 className="w-10 h-10 text-[#2EAF5D] animate-spin" />
          </div>

          <div className="bg-white rounded-lg p-4 border border-blue-200 space-y-2">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="w-4 h-4 text-blue-500" />
              <span>Domain: {domainConfig?.originalDomain || 'localhost'}</span>
            </div>
            <div className="text-xs text-blue-600 mt-2">
              {verificationStep}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error or not allowed
  if (!isAllowed || error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center space-y-6">
          <div className="mx-auto w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-10 h-10 text-red-600" />
          </div>

          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-red-900">
              Truy cập bị từ chối
            </h1>
            <p className="text-red-700">
              {error || 'Domain này không được phép truy cập ứng dụng'}
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 border border-red-200 space-y-2">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="w-4 h-4" />
              <span>Domain hiện tại: {domainConfig?.originalDomain || domainConfig?.domain || 'Unknown'}</span>
            </div>
            {domainConfig?.originalDomain && domainConfig?.mappedDomain && domainConfig.originalDomain !== domainConfig.mappedDomain && (
              <div className="text-xs text-blue-600">
                Mapped to: {domainConfig.mappedDomain}
              </div>
            )}
          </div>

          <div className="text-sm text-red-600">
            <p>Vui lòng liên hệ quản trị viên để được hỗ trợ</p>
          </div>
        </div>
      </div>
    )
  }

  // Domain verified and allowed
  return <>{children}</>
}
