"use client";

import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';

import Header from '@/components/header/Header';
import MainContent from '@/components/home/<USER>';
import { AppSidebar } from '@/components/sidebar/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { useGlobalState } from '@/hooks/useGlobalState';

import { ConversationItem } from '@/types/conversation';
import { fetchPromptconversations } from '@/services/userService';

export default function ChatPage() {
    const params = useParams();
    const router = useRouter();
    const { data: session, status } = useSession();
    const { auth } = useGlobalState();

    const [conversations, setConversations] = useState<ConversationItem[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // State for current conversation (can be different from URL)
    const [currentConversationId, setCurrentConversationId] = useState<string>(params.id as string);

    // Get conversation ID from URL params (initial load)
    const urlConversationId = params.id as string;

    console.log('🔗 [CHAT PAGE] URL conversation:', urlConversationId, 'Current conversation:', currentConversationId);

    // Sync state with URL on initial load
    useEffect(() => {
        if (urlConversationId !== currentConversationId) {
            setCurrentConversationId(urlConversationId);
        }
    }, [urlConversationId, currentConversationId]);

    // Listen for URL changes from sidebar navigation
    useEffect(() => {
        const handleUrlChange = () => {
            const pathParts = window.location.pathname.split('/');
            const newConversationId = pathParts[pathParts.length - 1];

            if (newConversationId && newConversationId !== currentConversationId) {
                console.log('🔄 [CHAT PAGE] URL changed to:', newConversationId);
                setCurrentConversationId(newConversationId);
            }
        };

        // Listen for both popstate (browser back/forward) and custom events
        window.addEventListener('popstate', handleUrlChange);

        // Listen for conversationChange events from other components (like ChatInterface)
        const handleConversationChange = (event: CustomEvent) => {
            const newConversationId = event.detail.conversationId;
            console.log('🔄 [CHAT PAGE] External conversation change event:', newConversationId);

            // Chỉ xử lý nếu conversation ID khác với hiện tại
            if (newConversationId !== currentConversationId) {
                console.log('🔄 [CHAT PAGE] Updating from external event:', newConversationId);
                setCurrentConversationId(newConversationId);
            }
        };

        window.addEventListener('conversationChange', handleConversationChange as EventListener);

        return () => {
            window.removeEventListener('popstate', handleUrlChange);
            window.removeEventListener('conversationChange', handleConversationChange as EventListener);
        };
    }, [currentConversationId]);

    // Handler for conversation switching
    const handleConversationSwitch = (newConversationId: string | null) => {
        if (!newConversationId) return;

        console.log('🔄 [CHAT PAGE] Switching conversation from', currentConversationId, 'to', newConversationId);

        // Cập nhật state
        setCurrentConversationId(newConversationId);

        // Cập nhật URL
        const newUrl = `/chat/${newConversationId}`;
        window.history.pushState({ conversationId: newConversationId }, '', newUrl);

        // Dispatch event để các component khác biết conversation đã thay đổi
        window.dispatchEvent(new CustomEvent('conversationChange', {
            detail: { conversationId: newConversationId }
        }));
    };

    // Load conversations when authenticated
    useEffect(() => {
        const loadConversations = async () => {
            if (!auth.isAuthenticated || !auth.token || !auth.prompt_id) {
                console.log('❌ Chat page: Not authenticated, skipping load');
                // Don't redirect here - let useAuth handle it
                return;
            }

            try {
                setIsLoading(true);
                console.log('📋 Loading conversations for chat page');

                const conversationsData = await fetchPromptconversations(auth.prompt_id, 1, 50, auth.token);
                const safeConversations = Array.isArray(conversationsData) ? conversationsData : [];

                setConversations(safeConversations);
                auth.setConversations(safeConversations);

                console.log('✅ Conversations loaded successfully:', safeConversations.length);
            } catch (error) {
                console.error('❌ Failed to load conversations:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadConversations();
    }, [auth.isAuthenticated, auth.token, auth.prompt_id, router]);



    // Loading state
    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Loading chat...</p>
                </div>
            </div>
        );
    }

    return (
        <SidebarProvider>
            <AppSidebar
                conversations={conversations}
                setConversations={setConversations}
                currentConversationId={currentConversationId}
                setCurrentConversationId={handleConversationSwitch}
            />
            <SidebarInset>

                <div className="flex flex-1 flex-col">
                    <Header />
                    <MainContent
                        conversations={conversations}
                        setConversations={setConversations}
                        currentConversationId={currentConversationId}
                        setCurrentConversationId={handleConversationSwitch}
                    />
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}
