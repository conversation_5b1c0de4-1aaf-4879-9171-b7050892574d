export enum EP {
    API = "api",
    V1 = "v1",
    WEBSITE = "website",
    INFO = "info",
    GLOBAL = "global",
    CATEGORIES = "categories",
    USER = "user",
    USERS = "users",
    PROFILE = "profile",
    SHOP = "shop",
    SHOPS = "shops",
    SHOP_USER = "shop-user",
    SHOP_USERS = "shop-users",
    VERIFY = "verify",
    KNOWLEDGE = "knowledge",
    DATASET = "dataset",
    DOCUMENT = "document",
    SEGMENT = "segment",
    CREATE = "create",
    UPDATE = "update",
    DELETE = "delete",
    DELETE_CONV = "deleteConv",
    LIST = "list",
    ESTIMATED = "estimated",
    PREVIEW = "preview",
    SSE = "sse",
    PROCESS = "process",
    TAG = "tag",
    PROMPT = "prompt",
    DOMAIN = "domain",
    CONVERSATION = "conversation",
    LOGIN = "login",
    GOOGLE = "google",
    ASSISTANT = "assistant",
    CHAT = "chat",
    MESSAGE = "message",
}
export enum QUERY {
    PAGE = "page",
    LIMIT = "limit",
    ID = "id",
    SHOP_ID = "shop_id",
    USER_ID = "user_id",
    TAG_ID = "tag_id",
    DOMAIN = "domain",
    PROMPT_ID = "prompt_id",
    CONVERSATION_ID = "conversation_id",
    EMAIL = "email",
    PASSWORD = "password",
}