import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Extract token and other data from request body
    const { token, shop_id, web_id, ...chatRequest } = body
    
    if (!token) {
      return NextResponse.json(
        { error: true, message: 'Token is required' },
        { status: 401 }
      )
    }

    // Forward request to the actual API
    const apiUrl = 'https://fchatai-api.salekit.com:3034/api/v1/message/chat'
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'token': token,
        ...(shop_id && { 'shop_id': shop_id }),
      },
      body: JSON.stringify({
        ...chatRequest,
        shop_id,
        web_id: web_id || shop_id,
      }),
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`)
    }

    // Return the streaming response
    return new NextResponse(response.body, {
      status: response.status,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })
  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: true, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
