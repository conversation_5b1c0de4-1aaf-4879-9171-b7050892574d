// Conversation utility functions

import type { ConversationItem } from '@/types/conversation';
import type { Conversation } from '@/services/userService';
import { getPromptIdWithFallback } from '@/lib/apiUtils';

// Utility function to ensure unique conversations
export const ensureUniqueConversations = (conversations: ConversationItem[]): ConversationItem[] => {
  const seen = new Set<string>();
  return conversations.filter(conversation => {
    if (seen.has(conversation.id)) {
      console.log('⚠️ Removing duplicate conversation with ID:', conversation.id);
      return false;
    }
    seen.add(conversation.id);
    return true;
  });
};

// Create a new conversation item
export const createNewConversation = (id: string = '0', title: string = '<PERSON>uộc trò chuyện mới'): ConversationItem => {
  return {
    id,
    icon: '/favicon.ico',
    title,
    messages: [],
    conversation: {
      _id: id,
      name: title,
      prompt_id: getPromptIdWithFallback(),
    }
  };
};

// Convert Conversation to ConversationItem
export const convertToConversationItem = (conversation: Conversation): ConversationItem => {
  return {
    id: conversation._id,
    title: conversation.name,
    icon: '/favicon.ico',
    messages: [],
    conversation
  };
};

// Filter conversations by search term
export const filterConversations = (conversations: ConversationItem[], searchTerm: string): ConversationItem[] => {
  if (!searchTerm.trim()) return conversations;
  
  const term = searchTerm.toLowerCase();
  return conversations.filter(conversation => 
    conversation.title.toLowerCase().includes(term) ||
    conversation.conversation?.name.toLowerCase().includes(term)
  );
};

// Sort conversations by date (newest first)
export const sortConversationsByDate = (conversations: ConversationItem[]): ConversationItem[] => {
  return [...conversations].sort((a, b) => {
    const dateA = new Date(a.conversation?.updated_at || a.conversation?.created_at || 0);
    const dateB = new Date(b.conversation?.updated_at || b.conversation?.created_at || 0);
    return dateB.getTime() - dateA.getTime();
  });
};

// Get conversation by ID
export const getConversationById = (conversations: ConversationItem[], id: string): ConversationItem | undefined => {
  return conversations.find(conversation => conversation.id === id);
};

// Update conversation in list
export const updateConversationInList = (
  conversations: ConversationItem[], 
  id: string, 
  updates: Partial<ConversationItem>
): ConversationItem[] => {
  return conversations.map(conversation => 
    conversation.id === id ? { ...conversation, ...updates } : conversation
  );
};

// Remove conversation from list
export const removeConversationFromList = (conversations: ConversationItem[], id: string): ConversationItem[] => {
  return conversations.filter(conversation => conversation.id !== id);
};

// Add conversation to list (at the beginning)
export const addConversationToList = (conversations: ConversationItem[], newConversation: ConversationItem): ConversationItem[] => {
  const filtered = conversations.filter(conversation => conversation.id !== newConversation.id);
  return [newConversation, ...filtered];
};

// Get recent conversations (last 10)
export const getRecentConversations = (conversations: ConversationItem[], limit: number = 10): ConversationItem[] => {
  return sortConversationsByDate(conversations).slice(0, limit);
};

// Check if conversation exists
export const conversationExists = (conversations: ConversationItem[], id: string): boolean => {
  return conversations.some(conversation => conversation.id === id);
};

// Get conversation count
export const getConversationCount = (conversations: ConversationItem[]): number => {
  return conversations.length;
};

// Validate conversation data
export const validateConversation = (conversation: Partial<ConversationItem>): boolean => {
  return !!(conversation.id && conversation.title);
};
