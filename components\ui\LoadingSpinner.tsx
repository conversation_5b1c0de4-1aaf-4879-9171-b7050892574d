import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

/**
 * Reusable loading spinner component
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className,
  text 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  return (
    <div className="flex items-center justify-center">
      <div className="text-center">
        <div 
          className={cn(
            'animate-spin rounded-full border-b-2 border-blue-500 mx-auto',
            sizeClasses[size],
            className
          )}
        />
        {text && (
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {text}
          </p>
        )}
      </div>
    </div>
  );
};

/**
 * Full screen loading overlay
 */
export const LoadingOverlay: React.FC<{ text?: string }> = ({ text }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <LoadingSpinner size="lg" text={text} />
      </div>
    </div>
  );
};

/**
 * Inline loading state for content areas
 */
export const LoadingContent: React.FC<{ text?: string; className?: string }> = ({ 
  text = "Đang tải...", 
  className 
}) => {
  return (
    <div className={cn("flex items-center justify-center h-full py-8", className)}>
      <LoadingSpinner text={text} />
    </div>
  );
};
