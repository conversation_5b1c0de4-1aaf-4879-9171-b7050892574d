"use client"

import { LogOut, User, Settings } from 'lucide-react'
import { useSession } from 'next-auth/react'
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'

import { performCompleteLogout } from '@/lib/logoutUtils'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { UserInfo } from '@/types/user'



export function UserProfile() {
  const { data: session, status } = useSession()
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check if user is logged in via Google or demo
  const isGoogle = !!session?.user
  const isDemoUser = sessionStorage.getItem('demo_logged_in') === 'true'
  const isLoggedIn = isGoogle || isDemoUser

  useEffect(() => {
    setIsLoading(true)

    // Luôn ưu tiên lấy user info từ auth store trước
    try {
      const authUserInfo = useAuthStore.getState().userInfo;
      if (authUserInfo) {
        setUserInfo(authUserInfo);
        setIsLoading(false);
        return;
      }
    } catch (error) {
      console.warn('Failed to get user info from auth store:', error);
    }

    if (session?.user) {
      // Google OAuth user - fallback nếu không có trong auth store
      const googleUserInfo = {
        name: session.user.name || '',
        email: session.user.email || '',
        image: session.user.image || ''
      }
      setUserInfo(googleUserInfo)

      // Cache user info in auth store
      try {
        useAuthStore.getState().setUserInfo({
          ...googleUserInfo,
          provider: 'google',
        });
      } catch (error) {
        console.warn('Failed to cache user info in auth store:', error);
      }

    } else if (isDemoUser) {
      // Demo user from auth store (no more sessionStorage)
      try {
        const authUserInfo = useAuthStore.getState().userInfo;
        if (authUserInfo && authUserInfo.provider !== 'google') {
          setUserInfo(authUserInfo);
        } else {
          setUserInfo(null);
        }
      } catch (error) {
        console.error('Error getting demo user info from auth store:', error);
        setUserInfo(null);
      }
    } else {
      // No user logged in
      setUserInfo(null)
    }

    setIsLoading(false)
  }, [session, isDemoUser])

  const handleSignOut = async () => {
    try {
      if (isGoogle) {
        // Clear auth store
        useAuthStore.getState().clearAll();
        toast.success('Đăng xuất thành công!')

        // Use the complete logout utility
        await performCompleteLogout()

      } else {
        // Demo user logout - clear from app store
        useAppStore.getState().clearDemoUser();
        useAuthStore.getState().clearAll();
        setUserInfo(null)
        toast.success('Đăng xuất thành công!')
        window.location.href = '/'
      }
    } catch (error) {
      console.error('Logout error:', error)
      toast.error('Có lỗi xảy ra khi đăng xuất')
    }
  }

  // Loading state
  if (status === 'loading' || isLoading) {
    return (
      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
    )
  }

  // Not logged in
  if (!isLoggedIn || !userInfo) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={userInfo.image || ''}
              alt={userInfo.name || 'User'}
            />
            <AvatarFallback>
              {userInfo.name?.charAt(0).toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {userInfo.name || 'User'}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {userInfo.email || ''}
            </p>
            {isGoogle && (
              <p className="text-xs text-green-600">Google Account</p>
            )}
            {isDemoUser && (
              <p className="text-xs text-blue-600">Demo Account</p>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Hồ sơ</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Cài đặt</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Đăng xuất</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
