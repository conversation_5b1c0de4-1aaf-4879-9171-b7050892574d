"use client"

import React, { useMemo } from 'react';

import { DomainGuard } from '@/components/auth/DomainGuard';
import Header from '@/components/header/Header';
import MainContent from '@/components/home/<USER>';
import { AppSidebar } from '@/components/sidebar/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { useAuth } from '@/hooks/useAuth';
import { useConversation } from '@/hooks/useConversation';
import { useProfile } from '@/hooks/useProfile';

/**
 * Home Page - Entry point for the application
 * Direct logic without AppFlow:
 * 1. Domain verification → prompt_id
 * 2. Check authentication
 * 3. Load profile + conversations
 * 4. Display chat interface
 */
export default function Home() {
    // Authentication and domain verification
    const { isReady, error: authError, isDomainVerifying } = useAuth();

    // Profile management
    const { isLoading: isProfileLoading, error: profileError } = useProfile(isReady);

    // Conversation management
    const {
        conversations,
        currentConversationId,
        isLoading: isConversationsLoading,
        error: conversationsError,
        handleConversationChange,
        setConversations
    } = useConversation(isReady);

    // Memoized loading state
    const isLoading = useMemo(() =>
        isDomainVerifying || isProfileLoading || isConversationsLoading,
        [isDomainVerifying, isProfileLoading, isConversationsLoading]
    );

    // Memoized error state
    const error = useMemo(() =>
        authError || profileError || conversationsError,
        [authError, profileError, conversationsError]
    );

    // Memoized conversations for sidebar (backward compatibility with "conversations")
    const memoizedConversationsForSidebar = useMemo(() => conversations, [conversations]);



    // Loading state
    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Loading application...</p>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-500 mb-4">Error: {error}</p>
                    <button
                        type="button"
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    // Main app interface
    return (
        <DomainGuard>
            <SidebarProvider>
                <AppSidebar
                    conversations={memoizedConversationsForSidebar}
                    setConversations={setConversations}
                    currentConversationId={currentConversationId}
                    setCurrentConversationId={handleConversationChange}
                />
                <SidebarInset>
                    <div className="flex flex-1 flex-col">
                        <Header />
                        <MainContent
                            conversations={memoizedConversationsForSidebar}
                            setConversations={setConversations}
                            currentConversationId={currentConversationId}
                            setCurrentConversationId={handleConversationChange}
                        />
                    </div>
                </SidebarInset>
            </SidebarProvider>
        </DomainGuard>
    );
}
