// Chat related types

/**
 * Chat message interface
 */
export interface Message {
    _id: string;
    type: 0 | 1; // 0: AI response, 1: User message
    content: string;
    albums: string[];
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    created_at: string;
    updated_at: string;
    conversation_id: string;
}

/**
 * Chat request payload
 */
export interface ChatRequest {
    conversation_id: string;
    query: string;
    prompt_id: string;
    shop_id: string;
    version: string;
    user_id: string;
    token: string;
    website_id?: string;
}

/**
 * Chat response from streaming API
 */
export interface ChatResponse {
    create_by: string;
    event: string;
    content: string;
    message: boolean;
}

/**
 * Chat payload for API calls
 */
export interface ChatPayload {
    conversation_id: string;
    query: string;
    prompt_id: string;
    shop_id: string;
    version: string;
    user_id: string;
    website_id: string;
}

export interface UseChatProps {
    conversationId: string;
    promptId?: string;
    shopId?: string;
    userId?: string;
    onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void;
}

export interface UseMessageChatProps {
    conversationId: string;
}

export interface UseSendChatProps {
    conversationId: string;
    promptId: string;
    shopId: string;
    userId: string;
    messages: any[];
    setMessages: React.Dispatch<React.SetStateAction<any[]>>;
    onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void;
    loadMessages: () => void;
}

export interface MessageStats {
    total: number;
    userMessages: number;
    aiMessages: number;
}

export interface ChatState {
    messages: Message[];
    messageStats: MessageStats;
    isLoadingMessages: boolean;
    messagesEndRef: React.RefObject<HTMLDivElement>;
    hasToken: boolean;
    isLoading: boolean;
    conversationInfo: { id?: string; name?: string } | null;
}

export interface ChatActions {
    handleSendMessage: (inputValue: string) => Promise<void>;
    loadMessages: () => Promise<void>;
    scrollToBottom: () => void;
    setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
}

export interface UseChatReturn extends ChatState, ChatActions {
    reload: () => Promise<void>;
}

export interface UseMessageChatReturn {
    messages: Message[];
    setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
    isLoadingMessages: boolean;
    messagesEndRef: React.RefObject<HTMLDivElement>;
    hasToken: boolean;
    loadMessages: () => Promise<void>;
    scrollToBottom: () => void;
}

export interface UseSendChatReturn {
    handleSendMessage: (inputValue: string) => Promise<void>;
    isLoading: boolean;
    conversationInfo: { id?: string; name?: string } | null;
}

// Chat component props (SendChat component)
export interface Chat {
    conversationId: string;
    promptId: string;
    shopId: string;
    userId: string;
    onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void;
}

// Streaming response types
export interface StreamingResponse {
    event: string;
    content?: string;
    conversation_id?: string;
    message_id?: string;
    message?: {
        conversation_id: string;
        message_id: string;
    };
}

// Token watcher types
export interface TokenWatcherState {
    hasToken: boolean;
    isChecking: boolean;
}