import { useMemo } from 'react';
import { useAuthData } from '@/hooks/useAuthData';
import { useDomain } from '@/components/providers/DomainProvider';

/**
 * Hook for domain-aware authentication
 * Combines domain verification with auth state
 */
export const useDomainAuth = () => {
    const { isAuthenticated, token, prompt_id } = useAuthData();
    const { 
        isVerifying: isDomainVerifying, 
        isAllowed, 
        error: domainError, 
        domainConfig 
    } = useDomain();

    const authState = useMemo(() => {
        // Wait for domain verification
        if (isDomainVerifying) {
            return {
                isReady: false,
                isAuthenticated: false,
                error: null,
                prompt_id: null,
                isDomainVerifying: true
            };
        }

        // Check if domain is allowed
        if (!isAllowed) {
            return {
                isReady: false,
                isAuthenticated: false,
                error: domainError || 'Domain không được phép truy cập',
                prompt_id: null,
                isDomainVerifying: false
            };
        }

        // Get prompt_id from domain config or auth store
        const finalPromptId = domainConfig?.prompt_id || prompt_id;
        
        // Wait for prompt_id
        if (!finalPromptId) {
            return {
                isReady: false,
                isAuthenticated: false,
                error: null,
                prompt_id: null,
                isDomainVerifying: false
            };
        }

        // Check authentication
        if (!isAuthenticated || !token) {
            return {
                isReady: false,
                isAuthenticated: false,
                error: null,
                prompt_id: finalPromptId,
                isDomainVerifying: false
            };
        }

        // All checks passed
        return {
            isReady: true,
            isAuthenticated: true,
            error: null,
            prompt_id: finalPromptId,
            isDomainVerifying: false
        };
    }, [
        isDomainVerifying,
        isAllowed,
        domainError,
        domainConfig?.prompt_id,
        prompt_id,
        isAuthenticated,
        token
    ]);

    return authState;
};

/**
 * Hook for checking if user needs to login
 */
export const useLoginRequired = () => {
    const { isReady, isAuthenticated, error } = useDomainAuth();
    
    return {
        needsLogin: isReady && !isAuthenticated && !error,
        canProceed: isReady && isAuthenticated,
        hasError: !!error,
        error
    };
};
