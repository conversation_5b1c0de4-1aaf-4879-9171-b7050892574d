/**
 * Centralized exports for all library utilities
 * Provides clean imports for the refactored codebase
 */

// Re-export types
export * from '@/types';

// Re-export hooks
export { useAuthData, useApiHeaders, useChatPayload } from '@/hooks/useAuthData';
export { useDomainAuth, useLoginRequired } from '@/hooks/useDomainAuth';
export { useMessages, useMessageStats } from '@/hooks/useMessages';
export { useMessageChat } from '@/hooks/useChat';
export { useSendChatForm } from '@/hooks/useSendChatForm';

// Re-export optimized auth hooks
export {
  useAuth,
  useAuthState,
  useAuthActions,
  useAuthSelector,
  useToken,
  useUserData,
  useAuthStatus,
  useUserCredits
} from '@/hooks/useAuthStore';

// Re-export services
export { BaseService, ServiceUtils } from '@/services/baseService';
export { ChatService, chatUtils } from '@/services/chatService';
export { ConversationService } from '@/services/conversationService';

// Re-export UI components
export { LoadingSpinner, LoadingOverlay, LoadingContent } from '@/components/ui/LoadingSpinner';
export { EmptyState, EmptyMessages, EmptyConversations } from '@/components/ui/EmptyState';
export { ChatInput } from '@/components/ui/ChatInput';
export { MessageList } from '@/components/chat/MessageList';

// Re-export stores
export { useAuthStore, authSelectors } from '@/stores/authStore.v2';

// Utility functions
export { apiCall, handleApiError, createApiClient } from '@/lib/apiUtils';
export { cn } from '@/lib/utils';

// Constants
export { EP, QUERY } from '@/configs/constants/api';

/**
 * Common patterns and utilities
 */
export const patterns = {
  // Common loading states
  createLoadingState: (isLoading: boolean, text?: string) => ({
    isLoading,
    text: text || 'Đang tải...'
  }),

  // Common error states
  createErrorState: (error: string | null, context?: string) => ({
    hasError: !!error,
    error: error ? `${context ? context + ': ' : ''}${error}` : null
  }),

  // Common empty states
  createEmptyState: (isEmpty: boolean, message?: string) => ({
    isEmpty,
    message: message || 'Không có dữ liệu'
  })
};

/**
 * Migration helpers for backward compatibility
 */
export const migration = {
  // Legacy hook wrapper
  useGlobalState: () => {
    console.warn('useGlobalState is deprecated. Use specific hooks like useAuth, useAuthData instead.');
    const auth = useAuth();
    return { auth };
  },

  // Legacy service wrapper
  legacyApiCall: apiCall,
  
  // Legacy component props mapper
  mapLegacyProps: (legacyProps: any) => {
    console.warn('Legacy props detected. Consider updating to new component API.');
    return legacyProps;
  }
};

/**
 * Development utilities
 */
export const dev = {
  // Debug store state
  debugAuthStore: () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Auth Store State:', useAuthStore.getState());
    }
  },

  // Performance monitoring
  measurePerformance: (name: string, fn: () => void) => {
    if (process.env.NODE_ENV === 'development') {
      const start = performance.now();
      fn();
      const end = performance.now();
      console.log(`${name} took ${end - start} milliseconds`);
    } else {
      fn();
    }
  },

  // Component render tracking
  trackRender: (componentName: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} rendered at ${new Date().toISOString()}`);
    }
  }
};
