import { useEffect, useState, useCallback, useMemo } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useGlobalState } from '@/hooks/useGlobalState'
import { useDomain } from '@/components/providers/DomainProvider'

export const useAuth = () => {
  const router = useRouter()
  const pathname = usePathname()
  const { auth } = useGlobalState()
  const { isVerifying: isDomainVerifying, isAllowed, error: domainError, domainConfig } = useDomain()
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [redirectAttempts, setRedirectAttempts] = useState(0)
  const [hasRedirected, setHasRedirected] = useState(false)
  const MAX_REDIRECT_ATTEMPTS = 3

  // Memoize auth values to prevent unnecessary re-renders
  // Only use prompt_id if domain verification is complete
  const authValues = useMemo(() => ({
    prompt_id: isDomainVerifying ? null : (domainConfig?.prompt_id || auth.prompt_id),
    isAuthenticated: auth.isAuthenticated,
    token: auth.token
  }), [auth.prompt_id, auth.isAuthenticated, auth.token, isDomainVerifying, domainConfig?.prompt_id])

  const checkAuth = useCallback(() => {
    console.log('🔍 useAuth checkAuth called:', {
      isDomainVerifying,
      isAllowed,
      domainConfig_prompt_id: domainConfig?.prompt_id,
      auth_prompt_id: auth.prompt_id,
      final_prompt_id: authValues.prompt_id,
      isAuthenticated: authValues.isAuthenticated,
      hasToken: !!authValues.token,
      pathname
    })

    // Wait for domain verification
    if (isDomainVerifying) {
      console.log('⏳ Domain still verifying...')
      setIsReady(false)
      return
    }

    // Check if domain is allowed
    if (!isAllowed) {
      console.log('❌ Domain not allowed')
      setError(domainError || 'Domain không được phép truy cập')
      setIsReady(false)
      return
    }

    // Wait for prompt_id from domain verification
    if (!authValues.prompt_id) {
      console.log('⏳ Waiting for prompt_id...', {
        isDomainVerifying,
        isAllowed,
        domainConfig_prompt_id: domainConfig?.prompt_id,
        auth_prompt_id: auth.prompt_id,
        'Domain verification must complete first': isDomainVerifying
      })
      setIsReady(false)
      return
    }

    // Check if user is authenticated
    if (!authValues.isAuthenticated || !authValues.token) {
      // Prevent infinite redirect loops
      if (redirectAttempts >= MAX_REDIRECT_ATTEMPTS || hasRedirected) {
        console.error('❌ Max redirect attempts reached or already redirected, stopping redirects')
        setError('Quá nhiều lần chuyển hướng. Vui lòng tải lại trang.')
        setIsReady(false)
        return
      }

      // Only redirect if we haven't already done so and we're not already on login page
      if (!hasRedirected && pathname !== '/login') {
        console.log('❌ User not authenticated, redirecting to login (attempt:', redirectAttempts + 1, ')')
        setRedirectAttempts(prev => prev + 1)
        setHasRedirected(true)

        // Use setTimeout to prevent immediate redirect loops
        setTimeout(() => {
          router.push('/login')
        }, 100)
      }

      setIsReady(false)
      return
    }

    // Reset redirect attempts and flag on successful authentication
    if (redirectAttempts > 0 || hasRedirected) {
      setRedirectAttempts(0)
      setHasRedirected(false)
    }

    // All checks passed
    setError(null)
    setIsReady(true)
  }, [isDomainVerifying, isAllowed, domainError, authValues.prompt_id, authValues.isAuthenticated, authValues.token, router])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return {
    isReady,
    isAuthenticated: auth.isAuthenticated,
    token: auth.token,
    prompt_id: auth.prompt_id,
    shop_id: auth.shop_id,
    user_id: auth.user_id,
    error,
    isDomainVerifying,
    isAllowed,
    // Auth actions
    setToken: auth.setToken,
    clearAuth: auth.clearAuth,
    setUserData: auth.setUserData
  }
}
