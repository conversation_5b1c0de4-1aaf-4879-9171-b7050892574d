import { ImageResponse } from 'next/og'

// Route segment config
export const runtime = 'edge'

// Image metadata
export const size = {
  width: 32,
  height: 32,
}
export const contentType = 'image/png'

// Image generation
export default function Icon() {
  return new ImageResponse(
    (
      <div
        tw="w-full h-full flex items-center justify-center text-white rounded-lg text-[24px] bg-brand-gradient"
      >
        F
      </div>
    ),
    {
      ...size,
    }
  )
}
