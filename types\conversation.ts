// Conversation related types

/**
 * Conversation entity
 */
export interface Conversation {
    _id: string;
    name?: string;
    prompt_id: string;
    created_at?: string;
    updated_at?: string;
}

/**
 * Request to create a new conversation
 */
export interface CreateConversationRequest {
    prompt_id: string;
    shop_id: string;
    user_id: string;
    name?: string;
}

export interface ConversationItem {
    id: string;
    icon: string;
    title: string;
    messages?: any[];
    conversation?: Conversation;
}

export interface ConversationUpdateDetail {
    oldId: string;
    newId: string;
    newName?: string;
}

export interface UseConversationProps {
    token?: string | null;
    currentConversationId?: string | null;
    setCurrentConversationId?: (id: string | null) => void;
    conversations?: ConversationItem[];
    setConversations?: (conversations: ConversationItem[] | ((prev: ConversationItem[]) => ConversationItem[])) => void;
}

export interface UseConversationUpdatesProps {
    onNewConversation: (newId: string, newName?: string) => void;
}

export interface ConversationMutations {
    addConversation: (newConversation: ConversationItem) => void;
    updateConversation: (conversationId: string, updates: Partial<ConversationItem>) => void;
    removeConversation: (conversationId: string) => void;
    reorderConversations: (newOrder: ConversationItem[] | ((prev: ConversationItem[]) => ConversationItem[])) => void;
    clearConversations: () => void;
}

export interface ConversationActions {
    loadConversations: () => Promise<void>;
    handleCreateNewConversation: () => Promise<void>;
    handleSaveConversationName: (conversationId: string, newName: string) => Promise<void>;
    handleDeleteConversation: (conversationId: string) => Promise<void>;
    handleConversationChange: (id: string | null) => void;
    handleConversationSelect: (topicId: string) => void;
}

export interface ConversationState {
    conversations: ConversationItem[];
    conversationsCount: number;
    recentConversations: ConversationItem[];
    currentConversationId: string | null;
    currentConversationInfo: {
        id: string | null;
        isNewConversation: boolean;
        chatUrl: string;
    };
    isLoading: boolean;
    error: string | null;
    editingId: string | null;
    editValue: string;
    isSaving: boolean;
    isCreatingNew: boolean;
}

export interface UseConversationReturn extends ConversationState, ConversationMutations, ConversationActions {
    // Navigation
    setCurrentConversationId: (id: string | null) => void;

    // Editing state
    setEditingId: (id: string | null) => void;
    setEditValue: (value: string) => void;

    // Utils
    useConversationUpdates: (props: UseConversationUpdatesProps) => void;
    reload: () => Promise<void>;
}