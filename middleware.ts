import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'


export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Handle old URL format: /?conversation_id=123 -> /chat/123
  if (url.pathname === '/' && url.searchParams.has('conversation_id')) {
    const conversationId = url.searchParams.get('conversation_id');
    if (conversationId) {
      console.log('🔄 Middleware: Redirecting old URL format to new format:', conversationId);
      url.pathname = `/chat/${conversationId}`;
      url.search = ''; // Remove query params
      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
