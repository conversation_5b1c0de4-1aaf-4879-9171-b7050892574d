# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here-change-this-in-production

# Google OAuth Configuration
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=1037683057246-g38hdvnob34u3ghth264t5lbhd4ioo08.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Site Configuration
NEXT_PUBLIC_URL=http://localhost:3000

# Google Site Verification (optional)
GOOGLE_SITE_VERIFICATION=your-google-site-verification-code

CRYPTOJS_SECRET_KEY="ultramailerCryptoTokenTrackEmail"
NEXT_PUBLIC_CRYPTOJS_SECRET_KEY="ultramailerCryptoTokenTrackEmail"

# API Configuration
NEXT_PUBLIC_WEBSITE_ID=""
NEXT_PUBLIC_REF=""