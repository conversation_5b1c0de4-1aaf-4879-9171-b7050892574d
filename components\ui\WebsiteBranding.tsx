"use client"

import React from 'react'
import Image from 'next/image'
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'

interface WebsiteBrandingProps {
  showTitle?: boolean
  showDescription?: boolean
  logoSize?: number
  className?: string
}

/**
 * Component hiển thị branding của website (logo + title) từ prompt.website
 * Dùng cho header, sidebar, hoặc các nơi cần hiển thị brand
 */
export const WebsiteBranding: React.FC<WebsiteBrandingProps> = ({
  showTitle = true,
  showDescription = false,
  logoSize = 32,
  className = ""
}) => {
  const {
    getTitle,
    getDescription,
    getLogo,
    isLoading
  } = useWebsiteInfo()

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div
          className="bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
          style={{ width: logoSize, height: logoSize }}
        />
        {showTitle && (
          <div className="w-20 h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
        )}
      </div>
    )
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Logo */}
      <Image
        src={getLogo()}
        alt={getTitle()}
        width={logoSize}
        height={logoSize}
        className="rounded object-contain"
        onError={(e) => {
          // Fallback nếu logo không load được
          const target = e.target as HTMLImageElement
          target.src = "/favicon.ico"
        }}
      />

      {/* Title và Description */}
      {(showTitle || showDescription) && (
        <div className="flex flex-col">
          {showTitle && (
            <span className="font-semibold text-lg leading-tight">
              {getTitle()}
            </span>
          )}
          {showDescription && (
            <span className="text-xs text-gray-500 leading-tight">
              {getDescription()}
            </span>
          )}
        </div>
      )}
    </div>
  )
}
