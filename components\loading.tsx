import clsx from 'clsx';
import React from 'react';

interface LoadingProps {
    size?: number; // chiều dài, rộng của spinner
    color?: string; // màu viền spinner
    borderWidth?: number; // độ dày viền spinner
    className?: string; // thêm class tùy chỉnh
}

const Loading: React.FC<LoadingProps> = ({
    size = 32,
    color = 'gray-500',
    borderWidth = 2,
    className = '',
}) => {
    const sizeClass = size <= 16 ? 'spinner-sm' : size <= 24 ? 'spinner-md' : size <= 32 ? 'spinner-lg' : 'spinner-xl';
    const borderClass = `border-b-${borderWidth}`;
    const colorClass = `border-${color}`;

    return (
        <div
            className={clsx(
                'animate-spin rounded-full mx-auto',
                sizeClass,
                borderClass,
                colorClass,
                className
            )}
        ></div>
    );
};

export default Loading;
