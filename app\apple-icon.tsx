import { ImageResponse } from 'next/og'

// Route segment config
export const runtime = 'edge'

// Image metadata
export const size = {
  width: 180,
  height: 180,
}
export const contentType = 'image/png'

// Image generation
export default function AppleIcon() {
  return new ImageResponse(
    (
      <div
        tw="w-full h-full flex items-center justify-center text-white rounded-lg text-[120px] font-bold bg-brand-gradient"
      >
        F
      </div>
    ),
    {
      ...size,
    }
  )
}
