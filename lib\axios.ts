import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import { getCookie } from "cookies-next/client";

import { useAuthStore } from "@/stores/authStore";
const axiosClient = (baseURL: string | undefined, token?: string) => {
    const axiosInstance: AxiosInstance = axios.create({
        responseType: "json",
        baseURL: baseURL,
        timeout: 10000,
        headers: {
            "Content-Type": "application/json",
        }
    })

    axiosInstance.interceptors.request.use(
        async (config) => {
            if (token) {
                config.headers.token = token;
            } else {
                // Get token from auth store or cookies
                let storedToken: string | undefined;

                // Try to get from auth store first
                try {
                    storedToken = useAuthStore.getState().getToken() || undefined;
                } catch (error) {
                    // Fallback to direct cookie access if store is not available
                    storedToken = getCookie('fchatai_token') as string || undefined;
                }

                if (storedToken) {
                    config.headers.token = storedToken;
                }
            }

            return config;
        },
        (error: AxiosError) => Promise.reject(error)
    );

    axiosInstance.interceptors.response.use(
        (response: AxiosResponse) => response,
        (error: AxiosError) => {
            // Handle authentication errors
            if (error.response && error.response.status === 401) {
                // Clear auth store on 401 errors
                try {
                    useAuthStore.getState().clearAll();
                } catch (e) {
                    // Fallback: clear cookies directly
                    if (typeof window !== 'undefined') {
                        document.cookie = 'fchatai_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        document.cookie = 'fchatai_user_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        document.cookie = 'fchatai_shop_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        // Note: prompt_id is not stored in cookies
                    }
                }
            }
            return Promise.reject(error);
        }
    );
    return axiosInstance;
}
export default axiosClient;