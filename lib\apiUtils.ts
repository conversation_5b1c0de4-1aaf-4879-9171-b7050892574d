// API utilities for authentication and HTTP requests
import { getCookie } from "cookies-next/client";

import { getApiEndpoint } from "@/helpers/handleApiUrl";
import axiosClient from "@/lib/axios";
import { useAuthStore } from "@/stores/authStore";
import { DataResponseType } from "@/types";


// Base API URL
export const BASE_API_URL = 'https://fchatai-api.salekit.com:3034';

/**
 * Clear all authentication tokens and user data
 */
export const clearAuthTokens = (): void => {
    try {
        // Use auth store to clear everything
        useAuthStore.getState().clearAll();
        console.log('🗑️ Cleared all authentication tokens via auth store');
    } catch (error) {
        // Fallback to manual clearing if store is not available
        if (typeof window !== 'undefined') {
            // Clear cookies only (no more localStorage)
            document.cookie = 'fchatai_auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax';
            document.cookie = 'fchatai_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax';
            document.cookie = 'fchatai_user_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax';
            document.cookie = 'fchatai_shop_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax';
            // Note: prompt_id is not stored in cookies

            console.log('🗑️ Cleared all authentication tokens via fallback');
        }
    }
};

/**
 * Save user authentication token using auth store
 */
export const saveTokenToCookie = (token: string): void => {
    console.log('🚀 API UTILS - saveTokenToCookie called with token:', token);
    console.log('🚀 API UTILS - Token type:', typeof token);
    console.log('🚀 API UTILS - Token length:', token?.length);

    try {
        // Use auth store to save token (it will handle cookies automatically)
        console.log('📞 API UTILS - Calling auth store setToken...');
        useAuthStore.getState().setToken(token);
        console.log('💾 API UTILS - Saved new authentication token via auth store');

        // Dispatch custom event to notify components immediately
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('tokenSet', { detail: { token } }));
            console.log('📡 API UTILS - Dispatched tokenSet event');
        }
    } catch (error) {
        console.error('❌ API UTILS - Auth store failed, using fallback:', error);
        // Fallback to manual saving if store is not available
        if (typeof window !== 'undefined') {
            // Clear any existing tokens first
            clearAuthTokens();

            const expires = new Date();
            expires.setDate(expires.getDate() + 7); // 7 days

            document.cookie = `fchatai_token=${token}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;

            console.log('💾 API UTILS - Saved new authentication token via fallback');

            // Dispatch custom event to notify components immediately
            window.dispatchEvent(new CustomEvent('tokenSet', { detail: { token } }));
            console.log('📡 API UTILS - Dispatched tokenSet event (fallback)');
        }
    }
};



/**
 * Validate if current token belongs to current user
 */
export const validateTokenForUser = (userEmail: string): boolean => {
    try {
        const authState = useAuthStore.getState();
        const storedUserEmail = authState.userInfo?.email;
        const hasToken = !!authState.getToken();

        if (!hasToken || !storedUserEmail) {
            return false;
        }

        if (storedUserEmail !== userEmail) {
            console.log('⚠️ Token belongs to different user, clearing...');
            clearAuthTokens();
            return false;
        }

        return true;
    } catch (error) {
        console.warn('Failed to validate token for user:', error);
        return false;
    }
};

/**
 * Get token from auth store with validation
 */
export const getStoredToken = (userEmail?: string): string | null => {
    try {
        // Get from auth store
        const authState = useAuthStore.getState();
        const token = authState.getToken();

        // If userEmail provided, validate token belongs to this user
        if (userEmail && token && !validateTokenForUser(userEmail)) {
            return null;
        }

        return token;
    } catch (error) {
        // Fallback to cookie if store is not available
        return getCookie('fchatai_token') as string || null;
    }
};

/**
 * Get shop_id from auth store
 */
export const getStoredShopId = (): string | null => {
    try {
        return useAuthStore.getState().shop_id;
    } catch (error) {
        return getCookie('fchatai_shop_id') as string || null;
    }
};

/**
 * Get prompt_id from auth store
 * Note: prompt_id is not stored in cookies, only in memory via domain verification
 */
export const getStoredPromptId = (): string | null => {
    try {
        return useAuthStore.getState().prompt_id;
    } catch (error) {
        return null; // No fallback to cookies for prompt_id
    }
};

/**
 * Get prompt_id with fallback
 */
export const getPromptIdWithFallback = (prompt_id?: string): string => {
    if (prompt_id) return prompt_id;

    const storedPromptId = getStoredPromptId();
    if (storedPromptId) return storedPromptId;

    return '1234567890';
};

/**
 * Create axios instance with token
 */
export const createApiClient = (token?: string, baseURL?: string) => {
    const finalToken = token || getStoredToken() || undefined;
    const finalBaseURL = baseURL || BASE_API_URL;
    return axiosClient(finalBaseURL, finalToken);
};

/**
 * Generic API call function
 */
export const apiCall = async <T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string[],
    data?: any,
    query?: Record<string, any>,
    token?: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    const finalToken = token || getStoredToken();
    const finalShopId = shop_id || getStoredShopId();

    if (!finalToken) {
        throw new Error('Token is required for API access');
    }

    const api = createApiClient(finalToken);
    const url = getApiEndpoint(endpoint, query);

    const config = {
        headers: {
            ...(finalShopId && { shop_id: finalShopId })
        }
    };

    console.log('🚀 Making API call:', {
        method,
        url: `${BASE_API_URL}${url}`,
        hasToken: !!finalToken,
        tokenPreview: finalToken ? finalToken.substring(0, 20) + '...' : 'NO_TOKEN',
        shop_id: finalShopId,
        headers: config.headers
    });

    try {
        let response;
        switch (method) {
            case 'GET':
                response = await api.get<DataResponseType<T>>(url, config);
                break;
            case 'POST':
                response = await api.post<DataResponseType<T>>(url, data, config);
                break;
            case 'PUT':
                response = await api.put<DataResponseType<T>>(url, data, config);
                break;
            case 'DELETE':
                response = await api.delete<DataResponseType<T>>(url, config);
                break;
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
        return response.data;
    } catch (error: any) {
        console.error(`API Error [${method} ${url}]:`, error.response?.data || error.message);
        throw error;
    }
};

/**
 * Handle API errors consistently
 */
export const handleApiError = (error: any, context: string): never => {
    const errorMessage = error.response?.data?.msg ||
        error.response?.data?.message ||
        error.message ||
        'Unknown error occurred';

    console.error(`❌ ${context} Error:`, error.response?.data || error.message);
    throw new Error(`${context}: ${errorMessage}`);
};

/**
 * Fetch with streaming support (for chat)
 */
export const fetchWithStream = async (
    url: string,
    data: any,
    token?: string,
    shop_id?: string,
    signal?: AbortSignal
): Promise<ReadableStream<Uint8Array> | null> => {
    const headers: Record<string, string> = {
        'Content-Type': 'application/json',
    };

    const finalToken = token || getStoredToken();
    const finalShopId = shop_id || getStoredShopId();

    // Only add token and shop_id headers if URL is external (not local proxy)
    if (!url.startsWith('/')) {
        if (finalToken) headers.token = finalToken;
        if (finalShopId) headers.shop_id = finalShopId;
    }

    try {
        console.log('🌐 Starting fetch request...');
        console.log('🔗 URL:', url);
        console.log('🌐 Headers:', headers);
        console.log('📤 Request body:', data ? JSON.stringify(data) : 'No body');

        const fetchOptions = {
            method: 'POST',
            headers,
            body: data ? JSON.stringify(data) : undefined,
            mode: 'cors' as RequestMode,
            credentials: 'omit' as RequestCredentials,
            redirect: 'follow' as RequestRedirect,
            signal
        };

        console.log('⚙️ Fetch options:', fetchOptions);

        const response = await fetch(url, fetchOptions);

        console.log('📥 Response received!');
        console.log('📥 Response status:', response.status, response.statusText);
        console.log('📋 Response headers:', Object.fromEntries(response.headers.entries()));
        console.log('🔍 Response type:', response.type);
        console.log('🔍 Response URL:', response.url);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ API Error response:', errorText);
            throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        console.log('✅ Response OK, returning stream body');
        return response.body;
    } catch (error) {
        console.error('❌ Error in fetchWithStream:', error);
        if (error instanceof Error) {
            console.error('❌ Error type:', error.constructor.name);
            console.error('❌ Error message:', error.message);
            if (error instanceof TypeError) {
                console.error('❌ This is likely a network/CORS issue');
            }
        }
        return null;
    }
};




//helo