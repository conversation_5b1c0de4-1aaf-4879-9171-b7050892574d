{
  "extends": [
    "next/core-web-vitals",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "rules": {
    // TypeScript specific rules
    "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],
    "@typescript-eslint/no-explicit-any": "off", // Allow any for now
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-empty-function": "off", // Allow empty functions
    "@typescript-eslint/no-inferrable-types": "off",

    // React specific rules
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react-hooks/exhaustive-deps": "off", // Turn off for now

    // General JavaScript rules
    "no-console": "off", // Allow console for debugging
    "no-debugger": "warn",
    "no-unused-vars": "off", // Use TypeScript version instead
    "prefer-const": "warn",
    "no-var": "error",

    // Import rules - simplified
    "import/order": "off", // Turn off for now

    // Next.js specific rules
    "@next/next/no-img-element": "off", // Allow img elements for now
    "@next/next/no-html-link-for-pages": "warn"
  },
  "env": {
    "browser": true,
    "es2020": true,
    "node": true
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "ignorePatterns": [
    "node_modules/",
    ".next/",
    "out/",
    "build/",
    "dist/",
    "*.config.js",
    "*.config.ts"
  ]
}
