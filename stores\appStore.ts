import { create } from 'zustand';
import { getCookie, setCookie, deleteCookie } from 'cookies-next/client';

// Types for all app data
interface AppState {
  // Domain configuration (replaces localStorage domain_config)
  domainConfig: any;

  // Google auth configuration
  googleAuthConfig: {
    enabled: boolean;
    clientId?: string;
    clientSecret?: string;
  };

  // Demo user state (replaces sessionStorage)
  demoUser: {
    isLoggedIn: boolean;
    userInfo: any;
  };

  // UI state
  isLoading: boolean;
  error: string | null;

  // Cross-tab sync state
  lastTokenUpdate: number;
}

interface AppActions {
  // Domain config management
  setDomainConfig: (config: any) => void;
  getDomainConfig: () => any;

  getPromptId: () => string | null; // <-- thêm dòng này
  getWebsiteId: () => string | null

  // Google auth config
  setGoogleAuthConfig: (config: any) => void;
  getGoogleAuthConfig: () => any;

  // Demo user management
  setDemoUser: (userInfo: any) => void;
  clearDemoUser: () => void;
  isDemoUserLoggedIn: () => boolean;

  // UI state
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Cross-tab sync
  updateTokenTimestamp: () => void;
  getLastTokenUpdate: () => number;

  // Clear all data
  clearAll: () => void;
}

// Cookie keys for sensitive data
const APP_COOKIE_KEYS = {
  DOMAIN_CONFIG: 'fchatai_domain_config',
  GOOGLE_CONFIG: 'fchatai_google_config',
} as const;

// Cookie options
const COOKIE_OPTIONS = {
  maxAge: 7 * 24 * 60 * 60, // 7 days
  path: '/',
  sameSite: 'lax' as const,
  secure: process.env.NODE_ENV === 'production',
};

export const useAppStore = create<AppState & AppActions>()((set, get) => ({
  // Hàm chuẩn hóa lấy website_id từ domainConfig
  getWebsiteId: () => {
    const state = get();
    // Ưu tiên lấy từ domainConfig.config.website.website_id
    if (state.domainConfig?.config?.website?.website_id) {
      return state.domainConfig.config.website.website_id;
    }
    // Nếu domainConfig.website.website_id tồn tại (cấu trúc cũ)
    if (state.domainConfig?.website?.website_id) {
      return state.domainConfig.website.website_id;
    }
    // Nếu domainConfig.website_id tồn tại (cực kỳ cũ)
    if (state.domainConfig?.website_id) {
      return state.domainConfig.website_id;
    }
    return null;
  },
  getPromptId: () => {
    const state = get();
    return state.domainConfig?.prompt?._id || null;
  },
  // Initial state
  domainConfig: null,
  googleAuthConfig: {
    enabled: false,
  },
  demoUser: {
    isLoggedIn: false,
    userInfo: null,
  },
  isLoading: false,
  error: null,
  lastTokenUpdate: 0,

  // Domain config management
  setDomainConfig: (config) => {
    // Save to cookie for sensitive data
    if (config) {
      setCookie(APP_COOKIE_KEYS.DOMAIN_CONFIG, JSON.stringify(config), COOKIE_OPTIONS);
    }
    set({ domainConfig: config });
  },

  getDomainConfig: () => {
    const state = get();
    if (state.domainConfig) return state.domainConfig;

    // Fallback to cookie
    try {
      const cookieConfig = getCookie(APP_COOKIE_KEYS.DOMAIN_CONFIG) as string;
      if (cookieConfig) {
        const config = JSON.parse(cookieConfig);
        set({ domainConfig: config });
        return config;
      }
    } catch (error) {
      console.warn('Failed to parse domain config from cookie:', error);
    }

    return null;
  },

  // Google auth config
  setGoogleAuthConfig: (config) => {
    if (config) {
      setCookie(APP_COOKIE_KEYS.GOOGLE_CONFIG, JSON.stringify(config), COOKIE_OPTIONS);
    }
    set({ googleAuthConfig: config });
  },

  getGoogleAuthConfig: () => {
    const state = get();
    if (state.googleAuthConfig.enabled) return state.googleAuthConfig;

    // Fallback to cookie
    try {
      const cookieConfig = getCookie(APP_COOKIE_KEYS.GOOGLE_CONFIG) as string;
      if (cookieConfig) {
        const config = JSON.parse(cookieConfig);
        set({ googleAuthConfig: config });
        return config;
      }
    } catch (error) {
      console.warn('Failed to parse google config from cookie:', error);
    }

    // Fallback to environment variables
    return {
      enabled: !!(process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET),
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
      clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET,
    };
  },

  // Demo user management
  setDemoUser: (userInfo) => {
    set({
      demoUser: {
        isLoggedIn: true,
        userInfo,
      },
    });
  },

  clearDemoUser: () => {
    set({
      demoUser: {
        isLoggedIn: false,
        userInfo: null,
      },
    });
  },

  isDemoUserLoggedIn: () => {
    return get().demoUser.isLoggedIn;
  },

  // UI state
  setLoading: (isLoading) => {
    set({ isLoading });
  },

  setError: (error) => {
    set({ error });
  },

  // Cross-tab sync
  updateTokenTimestamp: () => {
    set({ lastTokenUpdate: Date.now() });
  },

  getLastTokenUpdate: () => {
    return get().lastTokenUpdate;
  },

  // Clear all data
  clearAll: () => {
    // Clear cookies
    deleteCookie(APP_COOKIE_KEYS.DOMAIN_CONFIG);
    deleteCookie(APP_COOKIE_KEYS.GOOGLE_CONFIG);

    set({
      domainConfig: null,
      googleAuthConfig: { enabled: false },
      demoUser: { isLoggedIn: false, userInfo: null },
      isLoading: false,
      error: null,
      lastTokenUpdate: 0,
    });
  },
}));

// Export cookie keys for use in other files
export { APP_COOKIE_KEYS, COOKIE_OPTIONS as APP_COOKIE_OPTIONS };
