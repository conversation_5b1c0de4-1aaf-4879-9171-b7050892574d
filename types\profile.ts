// Profile related types
import type { User, Shop, Package } from './user';

/**
 * User profile with nested entities
 */
export interface UserProfile {
  _id?: string;
  name?: string;
  email?: string;
  avatar?: string;
  user?: User;
  shop?: Shop;
  package?: Package;
  // Direct credit fields (fallback)
  credit?: number;
  credit_use?: number;
  credit_bonus?: number;
}

export interface ProfileInfo {
  name: string;
  email?: string;
  avatar?: string;
  shop?: any;
  package?: any;
  permissions: string[];
}

export interface UseProfileProps {
  isAuthReady: boolean;
}

export interface ProfileState {
  profile: UserProfile | null;
  profileInfo: ProfileInfo | null;
  isLoading: boolean;
  error: string | null;
}

export interface ProfileActions {
  loadProfile: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => void;
  clearProfile: () => void;
  reload: () => Promise<void>;
}

export interface UseProfileReturn extends ProfileState, ProfileActions { }

// Re-export types from user.ts for convenience
export type { Shop, Package } from './user';

// Permission related types
export interface Permission {
  id: string;
  name: string;
  description?: string;
  scope: string;
}

export interface UserPermissions {
  permissions: string[];
  roles?: string[];
  canAccess: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}
