import { EP, QUERY } from "@/configs/constants/api";
import { apiCall, handleApiError } from "@/lib/apiUtils";
import { DataResponseType } from "@/types";

/**
 * Verify domain
 * @param domain Domain to verify
 * @returns Domain verification response
 */
export const verifyDomain = async<T>(domain: string): Promise<DataResponseType<T>> => {
    try {
        return await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.GLOBAL, EP.PROMPT, EP.DOMAIN],
            undefined,
            { [QUERY.DOMAIN]: domain }
        );
    } catch (error) {
        return handleApiError(error, 'Domain Verification');
    }
};

/**
 * Get prompt by ID
 * @param prompt_id Prompt ID
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns Prompt data
 */
export const getPromptById = async<T>(
    prompt_id: string,
    token?: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        return await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.PROMPT],
            undefined,
            { [QUERY.ID]: prompt_id },
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Get Prompt');
    }
};

/**
 * Create new prompt
 * @param promptData Prompt data
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns Created prompt
 */
export const createPrompt = async<T>(
    promptData: any,
    token?: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        return await apiCall<T>(
            'POST',
            [EP.API, EP.V1, EP.PROMPT, EP.CREATE],
            promptData,
            undefined,
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Create Prompt');
    }
};

/**
 * Update prompt
 * @param prompt_id Prompt ID
 * @param promptData Updated prompt data
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns Updated prompt
 */
export const updatePrompt = async<T>(
    prompt_id: string,
    promptData: any,
    token?: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        return await apiCall<T>(
            'PUT',
            [EP.API, EP.V1, EP.PROMPT, EP.UPDATE],
            { ...promptData, id: prompt_id },
            undefined,
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Update Prompt');
    }
};

/**
 * Delete prompt
 * @param prompt_id Prompt ID
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns Deletion result
 */
export const deletePrompt = async<T>(
    prompt_id: string,
    token?: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        return await apiCall<T>(
            'DELETE',
            [EP.API, EP.V1, EP.PROMPT, EP.DELETE],
            undefined,
            { [QUERY.ID]: prompt_id },
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Delete Prompt');
    }
};

/**
 * Get prompt list
 * @param page Page number
 * @param limit Items per page
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns List of prompts
 */
export const getPromptList = async<T>(
    page: number = 1,
    limit: number = 10,
    token?: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        return await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.PROMPT, EP.LIST],
            undefined,
            {
                [QUERY.PAGE]: page.toString(),
                [QUERY.LIMIT]: limit.toString()
            },
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Get Prompt List');
    }
};
