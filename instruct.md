# Hướng dẫn Luồng Chức năng Web Chat AI

## 🏗️ Cấu trúc Tổng thể

```
app/chat/[id]/page.tsx → MainContent → ChatInterface → SendChat + MessageList
                     ↓
                  AppSidebar (conversations management)
```

## 📋 Luồng Chức năng Chính

### 1. 🚀 Khởi tạo Ứng dụng

```
URL: /chat/[id] → ChatPage → useSession (auth) → useGlobalState → AppSidebar + MainContent
```

**Components:**
- `app/chat/[id]/page.tsx` - Main page component
- `components/home/<USER>
- `components/sidebar/AppSidebar.tsx` - Sidebar with conversations

**Hooks:**
- `useSession` - NextAuth authentication
- `useGlobalState` - Global state management
- `useConversation` - Conversation management

### 2. 💬 Hiển thị Chat Interface

```
MainContent → ChatInterface → useMessageChat → useMessages → ChatService.getMessages → API
                           ↓
                        MessageList (hiển thị messages)
                           ↓
                        SendChat (input form)
```

**Components:**
- `components/chat/ChatInterface.tsx` - Main chat container
- `components/chat/MessageList.tsx` - Display messages
- `components/chat/sendChat.tsx` - Input form

**Hooks:**
- `useMessageChat` → `useMessages` - Message state management
- `useCreditMonitor` - Credit monitoring
- `useWebsiteInfo` - Website metadata

**Types:**
- `ChatInterfaceProps` - Props cho ChatInterface
- `Message[]` - Array of messages
- `UseMessageChatReturn` - Return type của useMessageChat

### 3. 📝 Gửi Tin nhắn

```
User Input → ChatInput → useSendChatForm → handleSubmit → ChatService.sendMessage → API Stream
                                        ↓
                                   setMessages (add user message)
                                        ↓
                                   Process AI Response Stream
                                        ↓
                                   setMessages (add AI message)
```

**Components:**
- `components/ui/ChatInput.tsx` - Input component
- `components/ui/CreditWarning.tsx` - Credit warning

**Hooks:**
- `useSendChatForm` - Form logic
- `useAuthData` - Auth data (token, shop_id, user_id)
- `useChatPayload` - Create chat payload
- `useUserCredits` - Credit management

**Services:**
- `ChatService.sendMessage` - Send message to API
- `ChatService.createTempUserMessage` - Create temp user message

**Types:**
- `ChatPayload` - Request payload
- `SendChatComponentProps` - Props cho SendChat
- `UseSendChatReturn` - Return type

### 4. 🔄 Quản lý Conversations

```
Sidebar → "Bắt đầu chủ đề mới" → handleCreateNewConversation → setCurrentConversationId('0')
                                                            ↓
                                                    dispatch events (clearMessages, conversationChange)
                                                            ↓
                                                    router.replace('/chat/0')
                                                            ↓
                                                    ChatInterface updates
```

**Components:**
- `components/sidebar/AppSidebar.tsx` - Sidebar with conversation list

**Hooks:**
- `useConversation` - Conversation CRUD operations
- `useConversationState` - Global conversation state

**Events:**
- `clearMessages` - Clear messages immediately
- `conversationChange` - Notify conversation change
- `conversationUpdate` - Update conversation details

### 5. 📡 API Communication

```
Frontend → Services → API Routes → External API
```

**API Routes:**
- `/api/chat` - Proxy for chat messages (streaming)

**Services:**
- `services/chatService.ts` - Chat operations
- `services/userService.ts` - User operations
- `services/conversationService.ts` - Conversation operations

**Utils:**
- `lib/apiUtils.ts` - API utilities
- `lib/axios.ts` - HTTP client

## 🔗 Luồng Dữ liệu Chi tiết

### A. Load Messages
```
useMessages → ChatService.getMessages → GET /api/v1/message/conversation?conversation_id=X → setMessages
```

### B. Send Message
```
useSendChatForm → ChatService.sendMessage → POST /api/chat → Stream Response → Parse → setMessages
```

### C. Create New Conversation
```
handleCreateNewConversation → Create temp conversation (id='0') → Clear messages → Update URL → Dispatch events
```

### D. Switch Conversation
```
Sidebar click → setCurrentConversationId → useMessages effect → Load new messages → Update UI
```

## 🎯 Key Components & Responsibilities

| Component | Responsibility | Input | Output |
|-----------|---------------|-------|--------|
| `ChatInterface` | Main chat container | conversationId, promptId | Rendered chat UI |
| `MessageList` | Display messages | messages[], isLoading | Message bubbles |
| `SendChat` | Input form | conversationId, promptId | Form UI |
| `AppSidebar` | Conversation list | conversations[] | Sidebar UI |

## 🔧 Key Hooks & Functions

| Hook/Function | Purpose | Input | Output |
|---------------|---------|-------|--------|
| `useMessages` | Message state | conversationId | messages, setMessages, loading |
| `useSendChatForm` | Form logic | conversationId, promptId | inputValue, handleSubmit, isLoading |
| `useConversation` | Conversation CRUD | isAuthReady | conversations, handleCreateNew |
| `useAuthData` | Auth data | - | token, shop_id, user_id |

## 📊 State Management

```
Global State (Zustand) → useGlobalState → auth, conversations
                      ↓
Local State (useState) → currentConversationId, messages, inputValue
                      ↓
Events (CustomEvent) → conversationChange, clearMessages
```

## 🚨 Error Handling

```
API Error → handleApiError → Toast notification → Fallback UI
```

## 🔐 Authentication Flow

```
Login → NextAuth → Session → Token in Cookie → API Headers → Authenticated Requests
```

## 🔄 Luồng Streaming Response

```
User sends message → ChatService.sendMessage → POST /api/chat → External API Stream
                                            ↓
                                    ReadableStream → Reader → Chunk processing
                                            ↓
                                    Parse JSON chunks → Extract AI response
                                            ↓
                                    Update AI message in real-time → setMessages
```

## 📱 Event-Driven Architecture

### Custom Events:
- `clearMessages` - Clear messages immediately
- `conversationChange` - Switch conversation
- `conversationUpdate` - Update conversation metadata
- `conversationUpdated` - Notify sidebar of changes

### Event Flow:
```
User Action → Dispatch CustomEvent → Event Listeners → State Updates → UI Re-render
```

## 🎨 UI State Management

### Loading States:
- `isLoadingMessages` - Loading conversation messages
- `isLoading` - Sending message
- `isCreatingNew` - Creating new conversation

### Error States:
- API errors → Toast notifications
- Network errors → Retry mechanisms
- Auth errors → Redirect to login

## 🔧 Development Patterns

### Hook Composition:
```
useMessageChat → useMessages → useAuthData
                            → useCreditMonitor
                            → useWebsiteInfo
```

### Service Layer:
```
Components → Hooks → Services → API Utils → External APIs
```

### Type Safety:
```
TypeScript Interfaces → Runtime validation → API contracts
```
