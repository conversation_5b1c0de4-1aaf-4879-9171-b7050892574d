import { useMemo } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { useAppStore } from '@/stores/appStore';

/**
 * Hook to replace ALL localStorage usage with global state
 * This ensures no component uses localStorage directly
 */
export const useGlobalState = () => {
  const authStore = useAuthStore();
  const appStore = useAppStore();

  const auth = useMemo(() => ({
    token: authStore.token,
    user_id: authStore.user_id,
    shop_id: authStore.shop_id,
    prompt_id: authStore.prompt_id,
    userInfo: authStore.userInfo,
    isAuthenticated: authStore.isAuthenticated,
    profile: authStore.profile,
    conversations: authStore.conversations,
    website_id: authStore.getWebsiteId(),

    // Actions
    setToken: authStore.setToken,
    setUserData: authStore.setUserData,
    setUserInfo: authStore.setUserInfo,
    setProfile: authStore.setProfile,
    setConversations: authStore.setConversations,
    clearAuth: authStore.clearAll,
    getToken: authStore.getToken,
    getWebsite: authStore.getWebsiteId,

  }), [
    authStore.token,
    authStore.user_id,
    authStore.shop_id,
    authStore.prompt_id,
    authStore.userInfo,
    authStore.isAuthenticated,
    authStore.profile,
    authStore.conversations,
    authStore.setToken,
    authStore.setUserData,
    authStore.setUserInfo,
    authStore.setProfile,
    authStore.setConversations,
    authStore.clearAll,
    authStore.getToken,
  ]);

  const app = useMemo(() => ({
    domainConfig: appStore.domainConfig,
    googleAuthConfig: appStore.googleAuthConfig,
    demoUser: appStore.demoUser,
    isLoading: appStore.isLoading,
    error: appStore.error,

    // Actions
    setDomainConfig: appStore.setDomainConfig,
    getDomainConfig: appStore.getDomainConfig,
    setGoogleAuthConfig: appStore.setGoogleAuthConfig,
    getGoogleAuthConfig: appStore.getGoogleAuthConfig,
    setDemoUser: appStore.setDemoUser,
    clearDemoUser: appStore.clearDemoUser,
    isDemoUserLoggedIn: appStore.isDemoUserLoggedIn,
    setLoading: appStore.setLoading,
    setError: appStore.setError,
    clearApp: appStore.clearAll,
  }), [
    appStore.domainConfig,
    appStore.googleAuthConfig,
    appStore.demoUser,
    appStore.isLoading,
    appStore.error,
    appStore.setDomainConfig,
    appStore.getDomainConfig,
    appStore.setGoogleAuthConfig,
    appStore.getGoogleAuthConfig,
    appStore.setDemoUser,
    appStore.clearDemoUser,
    appStore.isDemoUserLoggedIn,
    appStore.setLoading,
    appStore.setError,
    appStore.clearAll,
  ]);

  const clearAll = useMemo(() => () => {
    authStore.clearAll();
    appStore.clearAll();
  }, [authStore.clearAll, appStore.clearAll]);

  return useMemo(() => ({
    auth,
    app,
    clearAll,
  }), [auth, app, clearAll]);
};
