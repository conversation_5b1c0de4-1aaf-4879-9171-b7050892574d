"use client"

import { useEffect } from 'react';

import { useAuthStore } from '@/stores/authStore';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider - Initializes auth store from cookies on app start
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const initializeFromCookies = useAuthStore((state) => state.initializeFromCookies);

  useEffect(() => {
    // Initialize auth store from cookies
    initializeFromCookies();
  }, [initializeFromCookies]);

  return <>{children}</>;
}
