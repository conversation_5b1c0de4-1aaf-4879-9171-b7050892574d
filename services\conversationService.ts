
import { EP } from "@/configs/constants/api";
import { apiCall, handleApiError } from "@/lib/apiUtils";
import { DataResponseType } from "@/types";
import { getCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';

export interface PayloadUpdateConversation {
    id: string;
    name: string;
}

/**
 * Update conversation (POST conversations)
 * Uses token from cookie as per requirement
 * @param payload Update conversation payload
 * @param shop_id Shop ID
 * @returns Updated conversation
 */
export const updateConversation = async<T>(
    payload: PayloadUpdateConversation,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        const token = getCookie(COOKIE_KEYS.TOKEN) as string;
        if (!token) {
            throw new Error('No authentication token found');
        }

        return await apiCall<T>(
            'POST',
            [EP.API, EP.V1, EP.CONVERSATION, EP.UPDATE],
            payload,
            undefined,
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Update Conversation');
    }
};

/**
 * Delete conversation (GET xóa conversations)
 * Uses token from cookie as per requirement
 * @param conversation_id Conversation ID
 * @param shop_id Shop ID
 * @returns Deletion result
 */
export const deleteConversation = async<T>(
    conversation_id: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        const token = getCookie(COOKIE_KEYS.TOKEN) as string;
        if (!token) {
            throw new Error('No authentication token found');
        }

        return await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.CONVERSATION, EP.DELETE_CONV, conversation_id],
            undefined,
            undefined, // No query params needed since ID is in path
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Delete Conversation');
    }
};

/**
 * Get conversation by ID (GET conversations)
 * Uses token from cookie as per requirement
 * @param conversation_id Conversation ID
 * @param shop_id Shop ID
 * @returns Conversation data
 */
export const getConversationById = async<T>(
    conversation_id: string,
    shop_id?: string
): Promise<DataResponseType<T>> => {
    try {
        const token = getCookie(COOKIE_KEYS.TOKEN) as string;
        if (!token) {
            throw new Error('No authentication token found');
        }

        return await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.CONVERSATION],
            undefined,
            { id: conversation_id },
            token,
            shop_id
        );
    } catch (error) {
        return handleApiError(error, 'Get Conversation');
    }
};