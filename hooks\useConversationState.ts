import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

interface ConversationState {
  currentConversationId: string | null;
  isLoading: boolean;

  // Actions
  setCurrentConversationId: (id: string | null) => void;
  setLoading: (loading: boolean) => void;
}

/**
 * Global conversation state store
 * Manages current conversation without causing page reloads
 */
export const useConversationStore = create<ConversationState>()(
  subscribeWithSelector((set) => ({
    currentConversationId: null,
    isLoading: false,

    setCurrentConversationId: (id: string | null) => {
      console.log('🔄 [GLOBAL STATE] Switching conversation to:', id);
      console.trace('🔍 Called from:');
      set({ currentConversationId: id });
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },
  }))
);

/**
 * Hook to use conversation state
 */
export const useConversationState = () => {
  const {
    currentConversationId,
    isLoading,
    setCurrentConversationId,
    setLoading
  } = useConversationStore();

  return {
    currentConversationId,
    isLoading,
    setCurrentConversationId,
    setLoading,
  };
};

/**
 * Hook to switch conversations without page reload
 */
export const useConversationSwitch = () => {
  const { setCurrentConversationId } = useConversationStore();

  const switchConversation = (conversationId: string | null) => {
    // Update global state without navigation
    setCurrentConversationId(conversationId);

    // Update URL without reload using history API
    if (typeof window !== 'undefined') {
      const newUrl = conversationId && conversationId !== '0'
        ? `/chat/${conversationId}`
        : '/chat/0';

      window.history.pushState(null, '', newUrl);
    }
  };

  return { switchConversation };
};
