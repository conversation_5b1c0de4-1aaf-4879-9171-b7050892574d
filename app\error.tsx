"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  const router = useRouter()

  useEffect(() => {
    // Log error for debugging
    console.error('Application error:', error)
  }, [error])

  const handleGoHome = () => {
    router.push('/')
  }

  const handleRetry = () => {
    reset()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-red-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-red-500 mb-4">400</h1>
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-2">
            <PERSON><PERSON><PERSON> ra lỗi
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            M<PERSON>y chủ không thể xử lý yêu cầu này vì yêu cầu không hợp lệ.
            Bạn không nên thử gửi lại yêu cầu.
          </p>
        </div>

        <div className="space-y-4">
          <button
            type="button"
            onClick={handleGoHome}
            className="w-full px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Về trang chủ
          </button>

          <button
            type="button"
            onClick={handleRetry}
            className="w-full px-6 py-3 border border-gray-300 text-gray-700 dark:text-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Thử lại
          </button>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="mt-8 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
              Chi tiết lỗi (Development)
            </summary>
            <pre className="mt-2 p-4 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
              {error.message}
              {error.stack && '\n\n' + error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  )
}
