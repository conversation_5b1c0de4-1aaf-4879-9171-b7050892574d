import { toast } from 'sonner'

/**
 * Utility functions for handling user credits
 */

export interface CreditInfo {
  credit: number
  credit_use: number
  credit_remaining: number
  percentage: number
  isOutOfCredit: boolean
  isLowCredit: boolean
}

/**
 * Tính toán thông tin credit
 */
export const calculateCreditInfo = (credit: number, credit_use: number): CreditInfo => {
  const credit_remaining = Math.max(0, credit - credit_use)
  const percentage = credit > 0 ? Math.round((credit_use / credit) * 100) : 0
  const isOutOfCredit = credit > 0 && credit_use >= credit
  const isLowCredit = credit > 0 && percentage >= 80 && !isOutOfCredit

  return {
    credit,
    credit_use,
    credit_remaining,
    percentage,
    isOutOfCredit,
    isLowCredit
  }
}

/**
 * Kiểm tra credit và hiển thị toast nếu hết credit
 * @param credit Tổng credit
 * @param credit_use Credit đã sử dụng
 * @returns true nếu có thể tiếp tục, false nếu hết credit
 */
export const checkCreditAndShowToast = (credit: number, credit_use: number): boolean => {
  const creditInfo = calculateCreditInfo(credit, credit_use)

  if (creditInfo.isOutOfCredit) {
    toast.error('Bạn đã dùng hết tin dụng chat', {
      description: `Bạn đã sử dụng ${credit_use.toLocaleString()}/${credit.toLocaleString()} credit. Vui lòng nạp thêm credit để tiếp tục chat.`,
      duration: 5000,
    })
    return false
  }

  return true
}

/**
 * Hiển thị cảnh báo credit thấp
 */
export const showLowCreditWarning = (credit: number, credit_use: number): void => {
  const creditInfo = calculateCreditInfo(credit, credit_use)

  if (creditInfo.isLowCredit) {
    toast.warning('Credit sắp hết', {
      description: `Còn lại ${creditInfo.credit_remaining.toLocaleString()}/${credit.toLocaleString()} credit (${100 - creditInfo.percentage}%). Hãy nạp thêm để tránh gián đoạn.`,
      duration: 4000,
    })
  }
}

/**
 * Format credit display
 */
export const formatCredit = (credit: number): string => {
  return credit.toLocaleString()
}

/**
 * Get credit status message
 */
export const getCreditStatusMessage = (credit: number, credit_use: number): string => {
  const creditInfo = calculateCreditInfo(credit, credit_use)

  if (creditInfo.isOutOfCredit) {
    return `Bạn đã dùng hết credit (${formatCredit(credit_use)}/${formatCredit(credit)}). Vui lòng nạp thêm để tiếp tục chat.`
  }

  if (creditInfo.isLowCredit) {
    return `Credit sắp hết. Còn lại ${formatCredit(creditInfo.credit_remaining)}/${formatCredit(credit)} credit.`
  }

  return `Còn lại ${formatCredit(creditInfo.credit_remaining)}/${formatCredit(credit)} credit.`
}

/**
 * Get placeholder text based on credit status
 */
export const getCreditPlaceholder = (credit: number, credit_use: number, defaultPlaceholder: string = "Hỏi bất kỳ điều gì bạn muốn..."): string => {
  const creditInfo = calculateCreditInfo(credit, credit_use)

  if (creditInfo.isOutOfCredit) {
    return `Bạn đã dùng hết credit (${formatCredit(credit_use)}/${formatCredit(credit)}). Vui lòng nạp thêm để tiếp tục chat.`
  }

  return defaultPlaceholder
}
