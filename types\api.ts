// API related types

/**
 * Standard API response format
 */
export interface DataResponseType<T = any> {
  error?: boolean;
  status: number;
  message?: string;
  msg?: string;
  data?: T;
  id?: string;
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T = any> extends DataResponseType<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * API error response
 */
export interface ApiError {
  success: false;
  message: string;
  error: string;
  statusCode: number;
}

/**
 * Pagination parameters for API requests
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * Domain verification response
 */
export interface DomainVerificationResponse {
  error: boolean;
  status: number;
  msg: string;
  data: string; // encrypted data
}

// Request types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface UpdateProfileRequest {
  name?: string;
  email?: string;
  avatar?: string;
}

export interface CreateConversationRequest {
  name: string;
  prompt_id: string;
}

export interface UpdateConversationRequest {
  name?: string;
}

export interface DeleteConversationRequest {
  conversation_id: string;
}

// Response types
export interface LoginResponse {
  token: string;
  user: {
    user_id: string;
    email: string;
    name: string;
  };
  shop?: {
    shop_id: string;
    name: string;
  };
  prompt_id?: string;
}

export interface ProfileResponse {
  user: UserProfile;
  shop?: Shop;
  package?: Package;
}

export interface ConversationsResponse {
  conversations: Conversation[];
  total: number;
}

export interface MessagesResponse {
  messages: Message[];
  conversation: Conversation;
}

// HTTP methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// API endpoints
export interface ApiEndpoints {
  auth: {
    login: string;
    register: string;
    logout: string;
    refresh: string;
  };
  profile: {
    get: string;
    update: string;
  };
  conversations: {
    list: string;
    create: string;
    get: (id: string) => string;
    update: (id: string) => string;
    delete: (id: string) => string;
  };
  messages: {
    list: (conversationId: string) => string;
    send: string;
  };
}

// Request configuration
export interface RequestConfig {
  method: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
}

// Import types from other files to avoid circular dependencies
import type { UserProfile } from './profile';
import type { Shop, Package } from './profile';
import type { Conversation } from './conversation';
import type { Message } from './chat';
