import { apiCall, handleApiError, createApiClient } from "@/lib/apiUtils";
import { DataResponseType } from "@/types";

/**
 * Base service class with common API functionality
 * Provides consistent error handling and request patterns
 */
export abstract class BaseService {
    /**
     * Make a GET request
     */
    protected static async get<T>(
        endpoint: string[],
        query?: Record<string, any>,
        token?: string,
        shop_id?: string
    ): Promise<DataResponseType<T>> {
        try {
            return await apiCall<T>('GET', endpoint, undefined, query, token, shop_id);
        } catch (error) {
            return handleApiError(error, `GET ${endpoint.join('/')}`);
        }
    }

    /**
     * Make a POST request
     */
    protected static async post<T>(
        endpoint: string[],
        data?: any,
        query?: Record<string, any>,
        token?: string,
        shop_id?: string
    ): Promise<DataResponseType<T>> {
        try {
            return await apiCall<T>('POST', endpoint, data, query, token, shop_id);
        } catch (error) {
            return handleApiError(error, `POST ${endpoint.join('/')}`);
        }
    }

    /**
     * Make a PUT request
     */
    protected static async put<T>(
        endpoint: string[],
        data?: any,
        query?: Record<string, any>,
        token?: string,
        shop_id?: string
    ): Promise<DataResponseType<T>> {
        try {
            return await apiCall<T>('PUT', endpoint, data, query, token, shop_id);
        } catch (error) {
            return handleApiError(error, `PUT ${endpoint.join('/')}`);
        }
    }

    /**
     * Make a DELETE request
     */
    protected static async delete<T>(
        endpoint: string[],
        query?: Record<string, any>,
        token?: string,
        shop_id?: string
    ): Promise<DataResponseType<T>> {
        try {
            return await apiCall<T>('DELETE', endpoint, undefined, query, token, shop_id);
        } catch (error) {
            return handleApiError(error, `DELETE ${endpoint.join('/')}`);
        }
    }

    /**
     * Create API client with authentication
     */
    protected static createClient(token?: string, baseURL?: string) {
        return createApiClient(token, baseURL);
    }

    /**
     * Validate required parameters
     */
    protected static validateRequired(params: Record<string, any>, requiredFields: string[]): void {
        const missing = requiredFields.filter(field => !params[field]);
        if (missing.length > 0) {
            throw new Error(`Missing required parameters: ${missing.join(', ')}`);
        }
    }

    /**
     * Format error response
     */
    protected static formatError(error: any, context: string): DataResponseType<never> {
        const message = error.response?.data?.msg || 
                       error.response?.data?.message || 
                       error.message || 
                       'Unknown error occurred';

        return {
            error: true,
            status: error.response?.status || 500,
            message: `${context}: ${message}`,
            data: undefined
        };
    }
}

/**
 * Service utilities for common operations
 */
export class ServiceUtils {
    /**
     * Retry a service call with exponential backoff
     */
    static async retry<T>(
        operation: () => Promise<T>,
        maxRetries: number = 3,
        baseDelay: number = 1000
    ): Promise<T> {
        let lastError: any;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                if (attempt === maxRetries) {
                    break;
                }
                
                const delay = baseDelay * Math.pow(2, attempt);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        throw lastError;
    }

    /**
     * Batch multiple service calls
     */
    static async batch<T>(
        operations: (() => Promise<T>)[],
        concurrency: number = 5
    ): Promise<T[]> {
        const results: T[] = [];
        
        for (let i = 0; i < operations.length; i += concurrency) {
            const batch = operations.slice(i, i + concurrency);
            const batchResults = await Promise.all(batch.map(op => op()));
            results.push(...batchResults);
        }
        
        return results;
    }

    /**
     * Cache service responses
     */
    private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

    static async cached<T>(
        key: string,
        operation: () => Promise<T>,
        ttl: number = 5 * 60 * 1000 // 5 minutes
    ): Promise<T> {
        const cached = this.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
            return cached.data;
        }
        
        const result = await operation();
        this.cache.set(key, {
            data: result,
            timestamp: Date.now(),
            ttl
        });
        
        return result;
    }

    /**
     * Clear cache
     */
    static clearCache(pattern?: string): void {
        if (pattern) {
            const regex = new RegExp(pattern);
            for (const key of this.cache.keys()) {
                if (regex.test(key)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }
}
