"use client"

import React from 'react'
import { AlertTriangle, Zap } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CreditWarningProps {
  credit: number
  credit_use: number
  className?: string
  showAlways?: boolean
}

/**
 * Component hiển thị cảnh báo credit
 * Hiển thị khi credit sắp hết hoặc đã hết
 */
export const CreditWarning: React.FC<CreditWarningProps> = ({
  credit,
  credit_use,
  className = "",
  showAlways = false
}) => {
  const credit_remaining = credit - credit_use
  const percentage = credit > 0 ? Math.round((credit_use / credit) * 100) : 0
  const isOutOfCredit = credit > 0 && credit_use >= credit
  const isLowCredit = credit > 0 && percentage >= 80 && !isOutOfCredit

  // Không hiển thị nếu không có credit data hoặc credit còn nhiều
  if (!credit || (!showAlways && !isLowCredit && !isOutOfCredit)) {
    return null
  }

  return (
    <div className={cn(
      "flex items-center gap-2 p-3 rounded-lg border text-sm",
      isOutOfCredit 
        ? "bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-800 dark:text-red-200"
        : isLowCredit
        ? "bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-200"
        : "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-200",
      className
    )}>
      {/* Icon */}
      <div className="flex-shrink-0">
        {isOutOfCredit ? (
          <AlertTriangle className="w-4 h-4" />
        ) : (
          <Zap className="w-4 h-4" />
        )}
      </div>

      {/* Content */}
      <div className="flex-1">
        {isOutOfCredit ? (
          <div>
            <div className="font-medium">Đã hết credit chat</div>
            <div className="text-xs opacity-80">
              Bạn đã sử dụng {credit_use.toLocaleString()}/{credit.toLocaleString()} credit. 
              Vui lòng nạp thêm để tiếp tục chat.
            </div>
          </div>
        ) : isLowCredit ? (
          <div>
            <div className="font-medium">Credit sắp hết</div>
            <div className="text-xs opacity-80">
              Còn lại {credit_remaining.toLocaleString()}/{credit.toLocaleString()} credit ({100 - percentage}%)
            </div>
          </div>
        ) : (
          <div>
            <div className="font-medium">Credit chat</div>
            <div className="text-xs opacity-80">
              Còn lại {credit_remaining.toLocaleString()}/{credit.toLocaleString()} credit ({100 - percentage}%)
            </div>
          </div>
        )}
      </div>

      {/* Progress bar */}
      <div className="flex-shrink-0 w-16">
        <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            className={cn(
              "h-full transition-all duration-300",
              isOutOfCredit
                ? "bg-red-500"
                : isLowCredit
                ? "bg-yellow-500"
                : "bg-blue-500"
            )}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
        <div className="text-xs text-center mt-1 opacity-70">
          {percentage}%
        </div>
      </div>
    </div>
  )
}
