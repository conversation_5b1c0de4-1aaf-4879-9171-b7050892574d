import * as React from "react"
import { useRef, useEffect } from "react"
import { cn } from "@/lib/utils"

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>(({ className, ...props }, forwardedRef) => {
  const innerRef = useRef<HTMLTextAreaElement>(null)
  const ref = (forwardedRef || innerRef) as React.RefObject<HTMLTextAreaElement>

  useEffect(() => {
    const textarea = ref.current
    if (!textarea) return

    const handleResize = () => {
      textarea.style.height = 'auto' // reset lại height về auto trước khi tính lại
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }

    handleResize()
    textarea.addEventListener("input", handleResize)

    return () => {
      textarea.removeEventListener("input", handleResize)
    }
  }, [ref])

  return (
    <textarea
      ref={ref}
      className={cn(
        "resize-none w-full rounded-md border border-input bg-background px-3 py-2 text-base placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className
      )}
      {...props}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
