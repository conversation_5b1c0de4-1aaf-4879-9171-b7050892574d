// Component props interfaces

import type { ConversationItem } from './conversation';

// MainContent component props
export interface MainContentProps {
  conversations: ConversationItem[];
  setConversations: (conversations: ConversationItem[] | ((prev: ConversationItem[]) => ConversationItem[])) => void;
  currentConversationId: string | null;
  setCurrentConversationId: (id: string | null) => void;
}

// AppSidebar component props
export interface AppSidebarProps {
  conversations: ConversationItem[];
  setConversations: (conversations: ConversationItem[] | ((prev: ConversationItem[]) => ConversationItem[])) => void;
  currentConversationId: string | null;
  setCurrentConversationId: (id: string | null) => void;
}

// Header component props
export interface HeaderProps {
  title?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

// ChatInterface component props
export interface ChatInterfaceProps {
  conversationId: string;
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void;
}

// Floating particles animation props
export interface FloatingParticle {
  top: string;
  left: string;
  size: number;
  delay: string;
}

// Common button props
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

// Modal props
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// Form field props
export interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'textarea';
  placeholder?: string;
  required?: boolean;
  error?: string;
  value?: string;
  onChange?: (value: string) => void;
}
