import { useState, useCallback, useEffect } from 'react'
import { useMessages } from '@/hooks/useMessages'
import { useAuthData, useChatPayload } from '@/hooks/useAuthData'
import { useUserCredits } from '@/hooks/useAuthStore'
import { checkCreditAndShowToast } from '@/lib/creditUtils'
import { ChatService, chatUtils } from '@/services/chatService'
import { useToast } from '@/hooks/use-toast'
import type { Message } from '@/types'

interface UseSendChatFormProps {
  conversationId: string
  promptId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
  messages?: Message[]
  setMessages?: React.Dispatch<React.SetStateAction<Message[]>>
}

export const useSendChatForm = ({ conversationId, promptId, onConversationUpdate, messages: externalMessages, setMessages: externalSetMessages }: UseSendChatFormProps) => {
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [currentConversationId, setCurrentConversationId] = useState(conversationId)
  const { toast } = useToast()

  const { hasToken } = useAuthData()
  const { createPayload } = useChatPayload()
  const { credit, credit_use } = useUserCredits()

  const internalMessagesHook = useMessages(externalMessages ? '' : conversationId)
  const messages = externalMessages || internalMessagesHook.messages
  const setMessages = externalSetMessages || internalMessagesHook.setMessages

  useEffect(() => {
    setCurrentConversationId(conversationId);
  }, [conversationId]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading || !hasToken) return

    if (!checkCreditAndShowToast(credit, credit_use)) {
      return
    }

    const userMessage = ChatService.createTempUserMessage(inputValue, currentConversationId)
    console.log('👤 Created user message:', userMessage);

    setMessages((prev: Message[]) => [...prev, userMessage])
    setIsLoading(true)

    try {
      const payload = createPayload(currentConversationId, inputValue, promptId)
      const stream = await ChatService.sendMessage(payload)

      if (stream) {
        const reader = stream.getReader()
        let aiContent = ''
        const aiMessage = ChatService.createTempAIMessage(currentConversationId)
        setMessages((prev: Message[]) => [...prev, aiMessage])

        let newConversationId: string | null = null;
        let hasProcessedConversationUpdate = false;

        await chatUtils.processStreamingResponse(
          reader,
          (content) => {
            aiContent += content
            setMessages((prev: Message[]) => {
              const updatedMessages = prev.map((msg: Message) =>
                msg._id === aiMessage._id ? { ...msg, content: aiContent } : msg
              );
              return updatedMessages;
            })
          },
          (finalData) => {
            console.log('🏁 Stream ended, final data:', finalData);
            if (currentConversationId === '0' && !hasProcessedConversationUpdate && onConversationUpdate) {
              const extractedId = newConversationId ||
                (finalData?.message?.conversation_id) ||
                (finalData?.conversation_id) ||
                (finalData?._id) ||
                (finalData?.id);

              if (extractedId && extractedId !== '0') {
                console.log('✅ New conversation created with ID:', extractedId);
                newConversationId = extractedId;
                const newTitle = inputValue.length > 50
                  ? inputValue.substring(0, 50) + '...'
                  : inputValue;
                onConversationUpdate('0', extractedId, newTitle);
                setCurrentConversationId(extractedId);
                hasProcessedConversationUpdate = true;
              } else {
                console.log('🔍 Available fields:', Object.keys(finalData || {}));
              }
            }
          },
          (conversationId) => {
            if (conversationId && conversationId !== '0' && !newConversationId) {
              console.log('🚀 Conversation ID detected immediately:', conversationId);
              newConversationId = conversationId;
              if (!hasProcessedConversationUpdate && onConversationUpdate) {
                const newTitle = inputValue.length > 50
                  ? inputValue.substring(0, 50) + '...'
                  : inputValue;
                onConversationUpdate('0', conversationId, newTitle);
                setCurrentConversationId(conversationId);
                hasProcessedConversationUpdate = true;
              }
            }
          }
        )

        if (currentConversationId === '0' && !hasProcessedConversationUpdate && onConversationUpdate) {
          const aiMessageConversationId = aiMessage.conversation_id;
          if (aiMessageConversationId && aiMessageConversationId !== '0') {
            console.log('✅ Found conversation ID in AI message:', aiMessageConversationId);
            newConversationId = aiMessageConversationId;
            const newTitle = inputValue.length > 50
              ? inputValue.substring(0, 50) + '...'
              : inputValue;
            onConversationUpdate('0', aiMessageConversationId, newTitle);
            setCurrentConversationId(aiMessageConversationId);
            hasProcessedConversationUpdate = true;
          }
        }

        if (currentConversationId === '0' && hasProcessedConversationUpdate && newConversationId) {
          window.history.pushState({ conversationId: newConversationId }, '', `/chat/${newConversationId}`);
          window.dispatchEvent(new CustomEvent('conversationChange', {
            detail: { conversationId: newConversationId }
          }));
          setMessages((prev: Message[]) =>
            prev.map((msg: Message) =>
              msg.conversation_id === '0'
                ? { ...msg, conversation_id: newConversationId || "" }
                : msg
            )
          );
          setCurrentConversationId(newConversationId);
        }
      } else {
        throw new Error('No stream received from API');
      }
    } catch (error) {
      console.error('Error sending message:', error)
      setMessages((prev: Message[]) => prev.filter((msg: Message) => msg._id !== userMessage._id))
      toast({
        title: "Lỗi",
        description: "Không thể gửi tin nhắn. Vui lòng thử lại.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setInputValue('')
    }
  }, [inputValue, isLoading, hasToken, credit, credit_use, currentConversationId, promptId, createPayload, setMessages, onConversationUpdate, toast])

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }, [handleSubmit])

  const isOutOfCredit = credit > 0 && credit_use >= credit

  return {
    inputValue,
    setInputValue,
    isLoading,
    handleSubmit,
    handleKeyPress,
    credit,
    credit_use,
    isOutOfCredit
  }
}