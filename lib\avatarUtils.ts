/**
 * Utility functions for handling user avatars
 */

/**
 * Tạo avatar URL từ user ID
 * @param userId User ID
 * @param fallbackUrl Fallback URL nếu không có user ID
 * @returns Avatar URL
 */
export const createAvatarUrl = (userId?: string | null, fallbackUrl?: string | null): string => {
  if (userId) {
    return `https://fchat.ai/avatar/${userId}.png`;
  }
  return fallbackUrl || '';
};

/**
 * Trích xuất user ID từ profile object
 * @param profile Profile object
 * @returns User ID
 */
export const extractUserId = (profile: any): string | null => {
  if (!profile) return null;
  
  return profile.user?._id || 
         profile._id || 
         profile.user?.id || 
         profile.id || 
         null;
};

/**
 * Tạo avatar URL từ profile object
 * @param profile Profile object
 * @param fallbackUrl Fallback URL
 * @returns Avatar URL
 */
export const getAvatarFromProfile = (profile: any, fallbackUrl?: string | null): string => {
  const userId = extractUserId(profile);
  const originalAvatar = profile?.user?.avatar || profile?.avatar || profile?.profile_picture;
  
  return createAvatarUrl(userId, fallbackUrl || originalAvatar);
};
