import React from 'react'
import { ChatInput } from '@/components/ui/ChatInput'
import { CreditWarning } from '@/components/ui/CreditWarning'
import { useSendChatForm } from '@/hooks/useSendChatForm'
import { getCreditPlaceholder } from '@/lib/creditUtils'
import type { SendChatComponentProps } from '@/types/sendChat'

/**
 * SendChat component - handles chat message input and submission
 * Now uses the reusable ChatInput component
 */
export function SendChat({ conversationId, promptId, onConversationUpdate, messages, setMessages }: SendChatComponentProps) {
    const {
        inputValue,
        setInputValue,
        isLoading,
        handleSubmit,
        handleKeyPress,
        credit,
        credit_use,
        isOutOfCredit
    } = useSendChatForm({ conversationId, promptId, onConversationUpdate, messages, setMessages })

    // Tạo placeholder động dựa trên trạng thái credit
    const placeholder = getCreditPlaceholder(credit, credit_use, "Hỏi bất kỳ điều gì bạn muốn...")

    return (
        <div className="space-y-3">
            {/* Credit Warning - hiển thị khi credit sắp hết hoặc đã hết */}
            <CreditWarning
                credit={credit}
                credit_use={credit_use}
                className="mx-4"
            />

            {/* Chat Input */}
            <ChatInput
                value={inputValue}
                onChange={setInputValue}
                onSubmit={handleSubmit}
                onKeyDown={handleKeyPress}
                isLoading={isLoading}
                disabled={isOutOfCredit}
                placeholder={placeholder}
                showAttachments={!isOutOfCredit}
                showVoice={!isOutOfCredit}
            />
        </div>
    )
}
