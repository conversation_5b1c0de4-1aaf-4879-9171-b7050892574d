import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Copy, Check } from 'lucide-react';
import { LoadingContent } from '@/components/ui/LoadingSpinner';
import { EmptyMessages } from '@/components/ui/EmptyState';
import type { Message } from '@/types';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  className?: string;
}

/**
 * Message list component for displaying chat messages
 * Separated from ChatInterface for better reusability
 */
export const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  messagesEndRef,
  className = ""
}) => {
  // Loading state
  if (isLoading) {
    return (
      <div className={`flex-1 overflow-y-auto ${className}`}>
        <LoadingContent text="Đang tải tin nhắn..." />
      </div>
    );
  }

  // Empty state
  if (messages.length === 0) {
    return (
      <div className={`flex-1 overflow-y-auto ${className}`}>
        <EmptyMessages />
      </div>
    );
  }

  // Messages list
  return (
    <div className={`absolute inset-0 overflow-y-auto bg-transparent p-4 ${className}`}>
      <div className="mx-auto max-w-[1280px]">
        {[...messages]
          .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
          .map((message) => (
            <MessageItem key={message._id} message={message} />
          ))}
        <div ref={messagesEndRef} />
      </div>

    </div>
  );
};

/**
 * Individual message item component
 */
interface MessageItemProps {
  message: Message;
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.type === 1;

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} group`}>
      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} gap-2 max-w-[80%]`}>
        <div
          className={`p-3 rounded-2xl ${isUser
            ? 'bg-blue-500 text-white rounded-tr-none'
            : ''
            }`}
        >
          {isUser ? (
            <UserMessage content={message.content} />
          ) : (
            <AIMessage content={message.content} />
          )}
        </div>
        <CopyButton content={message.content} />
      </div>
    </div>
  );
};

/**
 * User message component
 */
const UserMessage: React.FC<{ content: string }> = ({ content }) => {
  return (
    <p className="whitespace-pre-wrap break-words">
      {content}
    </p>
  );
};

/**
 * AI message component with markdown support
 */
const AIMessage: React.FC<{ content: string }> = ({ content }) => {
  // Handle null/undefined content
  if (!content) {
    return <div className="prose prose-sm max-w-none dark:prose-invert"></div>;
  }

  const processedContent = content
    .replace(/\n\n/g, '\n\n') // Keep double line breaks
    .replace(/\n(?!\n)/g, '  \n'); // Convert single line breaks to markdown line breaks

  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

/**
 * Copy button component for copying message content
 */
interface CopyButtonProps {
  content: string;
}

const CopyButton: React.FC<CopyButtonProps> = ({ content }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // Reset sau 2 giây
    } catch (err) {
      console.error('Không thể copy tin nhắn:', err);
    }
  };

  return (
    <button
      type="button"
      onClick={handleCopy}
      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
      title={copied ? "Đã copy!" : "Copy tin nhắn"}
    >
      {copied ? (
        <Check className="w-4 h-4 text-green-500" />
      ) : (
        <Copy className="w-4 h-4 text-gray-500 dark:text-gray-400" />
      )}
    </button>
  );
};
