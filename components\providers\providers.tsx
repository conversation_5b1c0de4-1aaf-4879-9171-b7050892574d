"use client"

import { SessionProvider } from "next-auth/react"
import type React from "react"

import { AuthProvider } from "@/components/providers/AuthProvider"
import { DomainProvider } from "@/components/providers/DomainProvider"
import { ThemeProvider } from "@/components/theme-provider"


export function Providers({
  children,
  session
}: {
  children: React.ReactNode
  session?: any
}) {
  return (
    <SessionProvider
      session={session}
      refetchInterval={0} // Disable automatic refetch
      refetchOnWindowFocus={false} // Don't refetch on window focus
      refetchWhenOffline={false} // Don't refetch when offline
    >
      <AuthProvider>
        <DomainProvider>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            {children}
          </ThemeProvider>
        </DomainProvider>
      </AuthProvider>
    </SessionProvider>
  )
}
