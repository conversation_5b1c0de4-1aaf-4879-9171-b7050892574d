import { create } from 'zustand';
import { getCookie, setCookie, deleteCookie } from 'cookies-next/client';
import type { AuthState, AuthActions } from '@/types/auth';

// Cookie keys
const COOKIE_KEYS = {
  TOKEN: 'fchatai_token',
  USER_ID: 'fchatai_user_id',
  SHOP_ID: 'fchatai_shop_id',
  PROMPT_ID: 'fchatai_prompt_id',
} as const;

// Cookie options
const COOKIE_OPTIONS = {
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  path: '/',
  sameSite: 'lax' as const,
  secure: process.env.NODE_ENV === 'production',
};

export const useAuthStore = create<AuthState & AuthActions>()((set, get) => ({
  // Getter for website_id
  getWebsiteId: () => {
    const state = get();
    return state.website_id || '';
  },

  // Ưu tiên lấy user_id từ cookie, nếu không c<PERSON> thì lấy từ state
  getUserId: () => {
    try {
      const { getCookie } = require('cookies-next/client');
      const cookieUserId = getCookie('fchatai_user_id');
      console.log("Cookie user_id:", cookieUserId);
      if (cookieUserId) return cookieUserId;
    } catch (e) { }
    const state = get();
    return state.user_id || '';
  },
  setPromptId: (prompt_id: string) => {
    set({ prompt_id });
  },
  // Initial state
  token: null,
  user_id: null,
  shop_id: null,
  prompt_id: null,
  userInfo: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  profile: null,
  topics: [],
  conversations: [],
  domainConfig: null,

  // Token management
  setToken: (token: string) => {
    console.log('💾 AUTH STORE - Setting token:', token);
    console.log('💾 AUTH STORE - Token length:', token?.length);

    // Save to cookie
    setCookie(COOKIE_KEYS.TOKEN, token, COOKIE_OPTIONS);
    console.log('✅ AUTH STORE - Token saved to cookie');

    // Extract user data from JWT if possible
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      console.log('🔍 AUTH STORE - JWT Payload:', payload);

      const userData = {
        user_id: payload.user_id || payload.sub,
        shop_id: payload.shop_id,
      };
      console.log('👤 AUTH STORE - Extracted user data:', userData);

      // Save additional data to cookies
      if (userData.user_id) {
        setCookie(COOKIE_KEYS.USER_ID, userData.user_id, COOKIE_OPTIONS);
        console.log('✅ AUTH STORE - User ID saved to cookie:', userData.user_id);
      }
      if (userData.shop_id) {
        setCookie(COOKIE_KEYS.SHOP_ID, userData.shop_id, COOKIE_OPTIONS);
        console.log('✅ AUTH STORE - Shop ID saved to cookie:', userData.shop_id);
      }

      set({
        token,
        ...userData,
        isAuthenticated: true,
        error: null,
      });
      console.log('✅ AUTH STORE - State updated successfully');
    } catch (error) {
      console.error('❌ AUTH STORE - JWT parsing failed:', error);
      // If JWT parsing fails, just save the token
      set({
        token,
        isAuthenticated: true,
        error: null,
      });
      console.log('⚠️ AUTH STORE - Token saved without JWT parsing');
    }
  },

  clearToken: () => {
    // Clear cookies
    deleteCookie(COOKIE_KEYS.TOKEN);
    deleteCookie(COOKIE_KEYS.USER_ID);
    deleteCookie(COOKIE_KEYS.SHOP_ID);

    set({
      token: null,
      user_id: null,
      shop_id: null,
      isAuthenticated: false,
    });
  },

  getToken: () => {
    const state = get();
    if (state.token) return state.token;

    // Fallback to cookie
    return getCookie(COOKIE_KEYS.TOKEN) as string || null;
  },

  // User data management
  setUserData: (data) => {
    const updates: Partial<AuthState> = {};

    // Luôn lưu user_id vào cookie nếu có
    if (data.user_id !== undefined) {
      updates.user_id = data.user_id;
      if (data.user_id) {
        setCookie(COOKIE_KEYS.USER_ID, data.user_id, COOKIE_OPTIONS);
        console.log('✅ AUTH STORE - User ID saved to cookie:', data.user_id);
      }
    }

    if (data.shop_id !== undefined) {
      updates.shop_id = data.shop_id;
      if (data.shop_id) {
        setCookie(COOKIE_KEYS.SHOP_ID, data.shop_id, COOKIE_OPTIONS);
      }
    }

    if (data.prompt_id !== undefined) {
      updates.prompt_id = data.prompt_id;
      // Không lưu prompt_id vào cookie
    }

    set(updates);
  },

  setUserInfo: (userInfo) => {
    set({ userInfo });
  },

  // Profile management
  setProfile: (profile) => {
    set({ profile });
  },

  setTopics: (topics) => {
    set({ topics });
  },

  setConversations: (conversations) => {
    set({ conversations });
  },

  // Domain configuration management
  setDomainConfig: (domainConfig) => {
    set({ domainConfig });
  },

  // State management
  setLoading: (isLoading) => {
    set({ isLoading });
  },

  setError: (error) => {
    set({ error });
  },

  // Clear all data
  clearAll: () => {
    // Clear cookies (except prompt_id which is not stored in cookies)
    deleteCookie(COOKIE_KEYS.TOKEN);
    deleteCookie(COOKIE_KEYS.USER_ID);
    deleteCookie(COOKIE_KEYS.SHOP_ID);
    // Note: prompt_id is not stored in cookies

    set({
      token: null,
      user_id: null,
      shop_id: null,
      prompt_id: null,
      userInfo: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      profile: null,
      conversations: [],
      domainConfig: null,
    });
  },

  // Initialize from cookies
  initializeFromCookies: () => {
    const token = getCookie(COOKIE_KEYS.TOKEN) as string;
    const user_id = getCookie(COOKIE_KEYS.USER_ID) as string;
    const shop_id = getCookie(COOKIE_KEYS.SHOP_ID) as string;
    // Note: prompt_id is not loaded from cookies - it comes from domain verification

    set({
      token: token || null,
      user_id: user_id || null,
      shop_id: shop_id || null,
      prompt_id: null, // Always start with null, will be set by domain verification
      isAuthenticated: !!(token && user_id),
    });
  },
}));

// Export cookie keys for use in other files
export { COOKIE_KEYS, COOKIE_OPTIONS };
