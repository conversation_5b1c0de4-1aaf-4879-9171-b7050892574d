import { useAppStore } from '@/stores/appStore';
import { GoogleAuthConfig } from '@/types/auth';

export const getGoogleAuthConfig = (): GoogleAuthConfig => {
  if (typeof window === 'undefined') {
    // Server-side: use environment variables as fallback
    return {
      enabled: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    };
  }

  try {
    // Get domain config from app store instead of localStorage
    const domainConfig = useAppStore.getState().getDomainConfig();
    if (!domainConfig) {
      console.log('🔍 No domain config found, using environment variables');
      return {
        enabled: !!(process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET),
        clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
        clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET,
      };
    }
    const websiteConfig = domainConfig?.config?.website;

    console.log('🔍 Google auth config check:', {
      google_login: websiteConfig?.google_login,
      client_id: websiteConfig?.client_id,
      client_secret: websiteConfig?.client_secret
    });

    // Check google_login setting
    if (websiteConfig?.google_login === 0) {
      console.log('🚫 Google auth disabled by domain config (google_login = 0)');
      return { enabled: false };
    }

    // If google_login is 1 or 2, check client credentials
    if (websiteConfig?.google_login === 1 || websiteConfig?.google_login === 2) {
      const hasClientId = websiteConfig?.client_id &&
        websiteConfig.client_id.trim() !== '' &&
        websiteConfig.client_id !== 'undefined' &&
        websiteConfig.client_id !== 'null';

      const hasClientSecret = websiteConfig?.client_secret &&
        websiteConfig.client_secret.trim() !== '' &&
        websiteConfig.client_secret !== 'undefined' &&
        websiteConfig.client_secret !== 'null';

      if (!hasClientId || !hasClientSecret) {
        console.log('🚫 Google auth disabled - missing client credentials');
        return { enabled: false };
      }

      console.log('✅ Google auth enabled - using domain credentials');
      return {
        enabled: true,
        clientId: websiteConfig.client_id,
        clientSecret: websiteConfig.client_secret,
      };
    }

    // Default: use environment variables
    console.log('✅ Google auth enabled - using environment variables');
    return {
      enabled: !!(process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET),
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
      clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET,
    };

  } catch (error) {
    console.error('❌ Error checking Google auth config:', error);
    // Default to environment variables on error
    return {
      enabled: !!(process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET),
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
      clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET,
    };
  }
};

/**
 * Check if Google login is enabled for client-side
 */
export const isGoogleLoginEnabled = (): boolean => {
  const config = getGoogleAuthConfig();
  return config.enabled;
};
