import { signOut } from 'next-auth/react'
import { clearAuthTokens } from '@/lib/apiUtils'

/**
 * Performs a complete logout including Google session
 */
export const performCompleteLogout = async () => {
  try {
    console.log('🚪 Starting complete logout process...')

    // 1. Clear auth tokens
    clearAuthTokens()

    // 2. Clear all storage
    sessionStorage.clear()
    localStorage.clear()

    // 3. Clear all auth-related cookies
    document.cookie.split(";").forEach((c) => {
      const eqPos = c.indexOf("=")
      const name = eqPos > -1 ? c.substring(0, eqPos) : c
      const cookieName = name.trim()

      // Clear NextAuth cookies
      if (cookieName.includes('next-auth') || cookieName.includes('__Secure-next-auth') || cookieName.includes('__Host-next-auth')) {
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`
      }

      // Clear Google OAuth cookies
      if (cookieName.includes('oauth') || cookieName.includes('google') || cookieName.includes('accounts.google')) {
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.google.com`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.accounts.google.com`
      }
    })

    // 4. Sign out from NextAuth
    console.log('🔐 Signing out from NextAuth...')
    await signOut({
      redirect: false
    })

    // 5. Clear Google session (without redirect for localhost)
    console.log('🔓 Clearing Google session...')

    // For localhost development, don't redirect to Google logout
    // as it causes 400 error. Just redirect to home page.
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('🏠 Development environment - redirecting to home')
      window.location.href = '/'
    } else {
      // For production, use Google logout with proper domain
      const googleLogoutUrl = 'https://accounts.google.com/logout'
      const returnUrl = encodeURIComponent(`${window.location.origin}/`)
      window.location.href = `${googleLogoutUrl}?continue=${returnUrl}`
    }

  } catch (error) {
    console.error('❌ Complete logout error:', error)
    // Fallback: clear everything and redirect to home
    clearAuthTokens()
    sessionStorage.clear()
    localStorage.clear()
    window.location.href = '/'
  }
}

/**
 * Alternative logout method that opens Google logout in a new window
 */
export const performLogoutWithPopup = async () => {
  try {
    console.log('🚪 Starting logout with popup...')

    // Clear local data first
    clearAuthTokens()
    sessionStorage.clear()
    localStorage.clear()

    // Sign out from NextAuth
    await signOut({
      redirect: false
    })

    // For localhost, skip Google logout popup
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('🏠 Development environment - skipping Google logout popup')
      window.location.href = '/'
    } else {
      // Open Google logout in popup for production
      const googleLogoutUrl = 'https://accounts.google.com/logout'
      const popup = window.open(googleLogoutUrl, 'google-logout', 'width=500,height=600')

      // Wait for popup to close or timeout
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed)
          // Redirect to home after popup closes
          window.location.href = '/'
        }
      }, 1000)

      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkClosed)
        if (popup && !popup.closed) {
          popup.close()
        }
        window.location.href = '/'
      }, 10000)
    }

  } catch (error) {
    console.error('❌ Popup logout error:', error)
    window.location.href = '/'
  }
}
