import { useCallback } from 'react';
import { useAuthStore, authSelectors } from '@/stores/authStore.v2';
import { useShallow } from 'zustand/react/shallow';

/**
 * Optimized hook for auth store with selective subscriptions
 */
export const useAuth = () => {
  return useAuthStore(useShallow((state) => ({
    // Core auth data
    token: state.token,
    user_id: state.user_id,
    shop_id: state.shop_id,
    prompt_id: state.prompt_id,
    isAuthenticated: state.isAuthenticated,
    
    // User info
    userInfo: state.userInfo,
    profile: state.profile,
    
    // State
    isLoading: state.isLoading,
    error: state.error,
    
    // Computed values
    hasValidAuth: authSelectors.hasValidAuth(state),
    userCredits: authSelectors.userCredits(state),
    
    // Actions
    setToken: state.setToken,
    clearToken: state.clearToken,
    setUserData: state.setUserData,
    setUserInfo: state.setUserInfo,
    setProfile: state.setProfile,
    clearAll: state.clearAll,
    
    // Getters
    getToken: state.getToken,
    getUserId: state.getUserId,
    getShopId: state.getShopId,
    getWebsiteId: state.getWebsiteId,
  })));
};

/**
 * Hook for auth state only (no actions)
 */
export const useAuthState = () => {
  return useAuthStore(useShallow((state) => ({
    token: state.token,
    user_id: state.user_id,
    shop_id: state.shop_id,
    prompt_id: state.prompt_id,
    isAuthenticated: state.isAuthenticated,
    userInfo: state.userInfo,
    profile: state.profile,
    isLoading: state.isLoading,
    error: state.error,
    hasValidAuth: authSelectors.hasValidAuth(state),
    userCredits: authSelectors.userCredits(state),
  })));
};

/**
 * Hook for auth actions only
 */
export const useAuthActions = () => {
  return useAuthStore(useShallow((state) => ({
    setToken: state.setToken,
    clearToken: state.clearToken,
    setUserData: state.setUserData,
    setUserInfo: state.setUserInfo,
    setProfile: state.setProfile,
    setLoading: state.setLoading,
    setError: state.setError,
    clearAll: state.clearAll,
    initializeFromCookies: state.initializeFromCookies,
  })));
};

/**
 * Hook for specific auth selectors
 */
export const useAuthSelector = <T>(selector: (state: ReturnType<typeof useAuthStore.getState>) => T) => {
  return useAuthStore(selector);
};

/**
 * Hook for token management
 */
export const useToken = () => {
  return useAuthStore(useShallow((state) => ({
    token: state.token,
    hasToken: !!state.token,
    setToken: state.setToken,
    clearToken: state.clearToken,
    getToken: state.getToken,
  })));
};

/**
 * Hook for user data
 */
export const useUserData = () => {
  return useAuthStore(useShallow((state) => ({
    user_id: state.user_id,
    shop_id: state.shop_id,
    userInfo: state.userInfo,
    profile: state.profile,
    setUserData: state.setUserData,
    setUserInfo: state.setUserInfo,
    setProfile: state.setProfile,
    getUserId: state.getUserId,
    getShopId: state.getShopId,
  })));
};

/**
 * Hook for authentication status
 */
export const useAuthStatus = () => {
  return useAuthStore(useShallow((state) => ({
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    hasValidAuth: authSelectors.hasValidAuth(state),
  })));
};

/**
 * Hook for domain-related auth data
 */
export const useDomainAuth = () => {
  return useAuthStore(useShallow((state) => ({
    prompt_id: state.prompt_id,
    domainConfig: state.domainConfig,
    website_id: state.getWebsiteId(),
    setDomainConfig: state.setDomainConfig,
  })));
};

/**
 * Hook for user credits
 */
export const useUserCredits = () => {
  return useAuthStore(useShallow((state) => authSelectors.userCredits(state)));
};
