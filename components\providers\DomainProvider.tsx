"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/authStore';

import { verifyDomain, DomainConfig } from '@/services/userService'
import { useAppStore } from '@/stores/appStore';
import { Domain } from '@/types/auth'


const DomainContext = createContext<Domain>({
  domainConfig: null,
  isVerifying: true,
  isAllowed: false,
  error: null,
  verificationStep: 'Khởi tạo...',
  retryVerification: () => { }
})

export function useDomain() {
  return useContext(DomainContext)
}
interface DomainProviderProps {
  children: React.ReactNode
}

export function DomainProvider({ children }: DomainProviderProps) {
  const [domainConfig, setDomainConfig] = useState<DomainConfig | null>(null)
  const setGlobalDomainConfig = useAppStore((state) => state.setDomainConfig)
  const setPromptId = useAuthStore((state: any) => state.setPromptId)
  const [isVerifying, setIsVerifying] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [verificationStep, setVerificationStep] = useState<string>('Khởi tạo...')
  const [verificationAttempts, setVerificationAttempts] = useState(0)
  const MAX_VERIFICATION_ATTEMPTS = 3

  const verifyCurrentDomain = async () => {
    try {
      // Prevent excessive verification attempts
      if (verificationAttempts >= MAX_VERIFICATION_ATTEMPTS) {
        setError('Quá nhiều lần thử xác thực domain. Vui lòng tải lại trang.')
        setIsVerifying(false)
        return
      }

      setVerificationAttempts(prev => prev + 1)
      setIsVerifying(true)
      setError(null)
      setVerificationStep('Đang phân tích domain...')

      // Clear any existing prompt_id from auth store to ensure clean verification
      try {
        const { useAuthStore } = require('@/stores/authStore');
        const authStore = useAuthStore.getState();
        authStore.setUserData({ prompt_id: null });
        console.log('🧹 Cleared existing prompt_id from auth store before verification');
      } catch (error) {
        console.warn('Failed to clear prompt_id:', error);
      }

      // Get current domain
      const originalDomain = typeof window !== 'undefined' ? window.location.hostname : 'localhost'
      let domainToVerify = originalDomain

      // Map localhost to agent.trinhxuanthuy.id.vn for API verification
      if (originalDomain === 'localhost' || originalDomain === '127.0.0.1') {
        domainToVerify = 'agent.trinhxuanthuy.id.vn'
      }

      // Show mapping step
      await new Promise(resolve => setTimeout(resolve, 800))
      setVerificationStep('Đang kết nối với server...')

      // Show API call step
      await new Promise(resolve => setTimeout(resolve, 700))
      setVerificationStep('Đang xác thực quyền truy cập...')

      // All domains (including mapped localhost) must verify through API
      const config = await verifyDomain(domainToVerify)
      console.log('1: Domain verification result:', config);

      if (config) {
        setVerificationStep('Đang xử lý kết quả...')
        await new Promise(resolve => setTimeout(resolve, 500))

        const configWithMapping = {
          ...config,
          originalDomain: originalDomain,
          mappedDomain: domainToVerify !== originalDomain ? domainToVerify : undefined,
          prompt_id: config.prompt_id // Ensure prompt_id is preserved
        }

        setDomainConfig(configWithMapping)
        setGlobalDomainConfig(configWithMapping) // Lưu toàn bộ data vào Zustand
        if (configWithMapping.prompt_id) {
          setPromptId(configWithMapping.prompt_id) // Lưu prompt_id vào Zustand
        }

        if (!config.allowed) {
          setVerificationStep('Xác thực thất bại')
          setError('Domain không được phép truy cập ứng dụng này')
        } else {
          setVerificationStep('Xác thực thành công!')
          await new Promise(resolve => setTimeout(resolve, 200))
        }
      } else {
        setVerificationStep('Xác thực thất bại')
        setError('Không thể xác thực domain')
      }

    } catch (err: any) {
      console.error('Domain verification failed:', err)
      setError(err.message || 'Lỗi xác thực domain')
    } finally {
      setIsVerifying(false)
    }
  }

  useEffect(() => {
    verifyCurrentDomain()
  }, [])

  const retryVerification = () => {
    if (verificationAttempts >= MAX_VERIFICATION_ATTEMPTS) {
      console.log('❌ Max verification attempts reached, cannot retry')
      return
    }
    console.log('🔄 Retrying domain verification...')
    verifyCurrentDomain()
  }

  const isAllowed = domainConfig?.allowed || false

  return (
    <DomainContext.Provider value={{
      domainConfig,
      isVerifying,
      isAllowed,
      error,
      verificationStep,
      retryVerification
    }}>
      {children}
    </DomainContext.Provider>
  )
}
