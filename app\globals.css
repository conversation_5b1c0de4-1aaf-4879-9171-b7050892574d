@tailwind base;
@tailwind components;
@tailwind utilities;

/* Markdown content styling */
.markdown-content {
  @apply text-gray-800 dark:text-gray-200;
}

.markdown-content p {
  @apply mb-2 last:mb-0 leading-relaxed;
}

.markdown-content strong {
  @apply font-semibold text-gray-900 dark:text-white;
}

.markdown-content em {
  @apply italic;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  @apply font-bold text-gray-900 dark:text-white mb-2;
}

.markdown-content h1 {
  @apply text-xl mb-3;
}

.markdown-content h2 {
  @apply text-lg;
}

.markdown-content h3 {
  @apply text-base;
}

.markdown-content ul,
.markdown-content ol {
  @apply mb-2 space-y-1;
}

.markdown-content ul {
  @apply list-disc list-inside;
}

.markdown-content ol {
  @apply list-decimal list-inside;
}

.markdown-content li {
  @apply text-gray-700 dark:text-gray-300;
}

.markdown-content blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic my-2 text-gray-600 dark:text-gray-400;
}

.markdown-content code {
  @apply bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-800 dark:text-gray-200;
}

.markdown-content pre {
  @apply bg-gray-100 dark:bg-gray-800 p-3 rounded-lg overflow-x-auto my-2;
}

.markdown-content pre code {
  @apply bg-transparent p-0 text-sm font-mono text-gray-800 dark:text-gray-200;
}

.markdown-content a {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}

.markdown-content hr {
  @apply border-gray-300 dark:border-gray-600 my-4;
}

.markdown-content table {
  @apply min-w-full border border-gray-300 dark:border-gray-600 my-2;
}

.markdown-content th {
  @apply border border-gray-300 dark:border-gray-600 px-3 py-2 bg-gray-100 dark:bg-gray-800 font-semibold text-left;
}

.markdown-content td {
  @apply border border-gray-300 dark:border-gray-600 px-3 py-2;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 50%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 210 100% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Smooth theme transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Disable transitions for specific elements that shouldn't animate */
.no-transition {
  transition: none !important;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) transparent;
}

.dark .custom-scrollbar {
  scrollbar-color: rgb(75 85 99) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 3px;
  border: none;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgb(148 163 184);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Hide scrollbar for webkit browsers when not needed */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Custom scrollbar for general use */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(248 250 252);
}

.dark ::-webkit-scrollbar-track {
  background: rgb(17 24 39);
}

::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 4px;
  border: 1px solid rgb(248 250 252);
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
  border: 1px solid rgb(17 24 39);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(148 163 184);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

table {
  border-spacing: 0 !important;
  border-collapse: collapse;
  border-color: inherit !important;
  display: block !important;
  width: max-content !important;
  max-width: 100% !important;
  overflow: auto !important;
  margin-top: 1em;
  margin-bottom: 1em;
  font-size: 0.95rem;
}

thead {
  background-color: #f9fafb;
  text-align: left;
}

.prose th,
.prose td {
  border: 1px solid #e5e7eb;
  /* màu xám nhạt */
  padding: 0.75rem;
  vertical-align: top;
}

th {
  font-weight: 600;
  background-color: #f3f4f6;
}

/* Dark mode styles for table */
.dark table {
  border-color: #374151;
  /* màu xám đậm */
}

.dark thead {
  background-color: #1f2937;
  /* màu xám tối */
}

.dark .prose th,
.dark .prose td {
  border: 1px solid #4b5563;
  /* màu xám trung bình */
}

.dark th {
  background-color: #111827;
  /* màu xám đậm hơn */
  color: #d1d5db;
  /* màu chữ sáng */
}


.prose :where(a):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  text-decoration: none !important;
}

/* Loading spinner sizes */
.spinner-sm {
  height: 16px;
  width: 16px;
}

.spinner-md {
  height: 24px;
  width: 24px;
}

.spinner-lg {
  height: 32px;
  width: 32px;
}

.spinner-xl {
  height: 48px;
  width: 48px;
}