"use client"

import { Zap } from 'lucide-react'
import React from 'react'

interface CreditDisplayProps {
  credit?: number
  credit_use?: number
  className?: string
}

export function CreditDisplay({ credit = 0, credit_use = 0, className = "" }: CreditDisplayProps) {
  const credit_remaining = credit - credit_use
  const percentage = credit > 0 ? Math.round((credit_use / credit) * 100) : 0

  // Don't show if no credit data
  if (!credit) {
    return null
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="flex items-center space-x-1">
        <Zap className="w-4 h-4 text-[#2EAF5D]" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {credit_remaining.toLocaleString()}
        </span>
        <span className="text-xs text-gray-500">
          / {credit.toLocaleString()}
        </span>
      </div>

      {/* Progress bar */}
      <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div
          className="h-full bg-gradient-to-r from-[#2EAF5D] to-[#238B4F] transition-all duration-300"
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>

      <span className="text-xs text-gray-500">
        {percentage}%
      </span>
    </div>
  )
}
