"use client";

import { Plus, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { useConversation } from '@/hooks/useConversation';
import { useGlobalState } from '@/hooks/useGlobalState';
import type { AppSidebarProps } from '@/types/components';
import Image from 'next/image';
import type { ConversationItem } from '@/types';

export function AppSidebar({
  conversations,
  setConversations,
  currentConversationId,
  setCurrentConversationId,
  setMessages
}: AppSidebarProps & { setMessages: React.Dispatch<React.SetStateAction<any[]>> }) {
  const { auth } = useGlobalState();
  const token = auth.token;
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

  const conversationsHook = useConversation(true, {
    token,
    currentConversationId: currentConversationId,
    setCurrentConversationId: setCurrentConversationId,
    conversations: conversations,
    setConversations: setConversations
  });

  const { websiteInfo, isLoading } = useWebsiteInfo()

  // Listen for conversation updates
  conversationsHook.useConversationUpdates({
    onNewConversation: (newId: string, newName?: string) => {
      // Thêm cuộc trò chuyện mới vào danh sách
      const newConversation = {
        id: newId,
        icon: '/favicon.ico',
        title: newName || 'Cuộc trò chuyện mới',
        messages: [],
        conversation: {
          _id: newId,
          name: newName || 'Cuộc trò chuyện mới',
          prompt_id: auth.prompt_id || ''
        }
      };
      setConversations((prev: ConversationItem[]) => {
        const filteredConversations = prev.filter(conv => conv.id !== '0'); // Xóa cuộc trò chuyện tạm
        return [newConversation, ...filteredConversations];
      });
      // Active cuộc trò chuyện mới
      setCurrentConversationId(newId);
    }
  });

  // Initialize auth token
  React.useEffect(() => {
    if (!token) { }
  }, [token]);

  const handleEditStart = (conversationId: string, title: string) => {
    setOpenDropdownId(null);
    setEditingId(conversationId);
    setEditValue(title);
  };

  useEffect(() => {
    if (editingId && editInputRef.current) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          if (editInputRef.current) {
            editInputRef.current.focus();
            editInputRef.current.select();
          }
        });
      });
    }
  }, [editingId]);

  const handleEditSave = (conversationId: string) => {
    if (editValue.trim()) {
      conversationsHook.handleSaveConversationName(conversationId, editValue.trim());
    }
    setEditingId(null);
  };

  const handleEditCancel = () => {
    setEditingId(null);
    setEditValue('');
  };

  return (
    <Sidebar variant="inset">
      <div className="flex items-center gap-2 pb-2 pt-0.5 px-2">
        <Image src="/favicon.ico" alt="Logo Chat AI" height={50} width={50} />
      </div>
      <SidebarHeader>
        <div className="px-2">
          <div className="group relative flex cursor-pointer flex-row gap-3 rounded-2xl p-4 transition-all duration-200 ease-in-out bg-white hover:bg-[#e8e8e8a2] dark:bg-gray-900 dark:hover:bg-slate-700 border border-gray-200 dark:border-slate-600">
            <div className="flex overflow-hidden">
              <Image src={websiteInfo.logo} alt="Logo Chat AI" height={32} width={32} />
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="flex items-center gap-1.5">
                <p className="truncate text-sm font-semibold">{websiteInfo.meta_title}</p>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-500">
                  <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
                </svg>
              </div>
              <p className="truncate text-xs text-gray-500 dark:text-slate-400">{websiteInfo.meta_description}</p>
            </div>
          </div>
        </div>
      </SidebarHeader>
      <SidebarSeparator className='bg-gray-200 dark:bg-slate-700 my-3' />
      <SidebarContent className=''>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <button
                type="button"
                //update url về chat/0 ngay lập tức

                onClick={() => {
                  const newUrl = '/chat/';
                  window.history.pushState({ conversationId: '0' }, '', newUrl);
                  window.dispatchEvent(new CustomEvent('conversationChange', {
                    detail: { conversationId: '' }
                  }));
                  if (setCurrentConversationId) {
                    setCurrentConversationId('');
                  }
                  setMessages([]);
                }
                }
                disabled={conversationsHook.isCreatingNew}
                className='inline-flex items-center justify-center gap-2 mx-2 whitespace-nowrap text-sm font-medium rounded-2xl bg-blue-500 hover:bg-blue-600 py-2 text-white'>
                <Plus className="h-4 w-4" />
                <span>{conversationsHook.isCreatingNew ? 'Đang tạo...' : 'Bắt đầu chủ đề mới'}</span>
              </button>
              <SidebarGroupLabel className="text-blue-500">Chủ đề AI</SidebarGroupLabel>
              {conversationsHook.isLoading ? (
                <SidebarMenuItem>
                  <div className="flex items-center gap-2 px-3 py-3">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span className="text-sm text-white">Đang tải...</span>
                  </div>
                </SidebarMenuItem>
              ) : conversations.length === 0 ? (
                <SidebarMenuItem>
                  <div className="px-3 py-3 text-sm text-white/70">
                    Chưa có cuộc trò chuyện nào
                  </div>
                </SidebarMenuItem>
              ) : (
                conversations.map((conversation) => (
                  <SidebarMenuItem key={conversation.id} isActive={conversation.id === currentConversationId}>
                    <SidebarMenuButton
                      isActive={conversation.id === currentConversationId}
                      onClick={() => {
                        if (conversation.id === currentConversationId) {
                          return;
                        }
                        const newUrl = `/chat/${conversation.id}`;
                        window.history.pushState({ conversationId: conversation.id }, '', newUrl);
                        window.dispatchEvent(new CustomEvent('conversationChange', {
                          detail: { conversationId: conversation.id }
                        }));
                        if (setCurrentConversationId) {
                          setCurrentConversationId(conversation.id);
                        }
                      }}
                    >
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="relative">
                          <Image
                            src="/general_chat.webp"
                            alt="Chat AI"
                            width={24}
                            height={24}
                            className="rounded-full transition-all"
                          />
                          {conversation.id === currentConversationId && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full dark:border-gray-800"></div>
                          )}
                        </div>
                        {editingId === conversation.id ? (
                          <Input
                            id={`conversation-name-input-${conversation.id}`}
                            name="conversationName"
                            ref={editInputRef}
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            onBlur={() => handleEditSave(conversation.id)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleEditSave(conversation.id);
                              } else if (e.key === 'Escape') {
                                handleEditCancel();
                              }
                            }}
                            className="h-6 text-sm border-none p-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent"
                            disabled={conversationsHook.isSaving}
                          />
                        ) : (
                          <span className='truncate text-sm font-medium transition-colors'>
                            {conversation.title}
                          </span>
                        )}
                      </div>
                      <DropdownMenu
                        open={openDropdownId === conversation.id}
                        onOpenChange={(open) => setOpenDropdownId(open ? conversation.id : null)}
                      >
                        <DropdownMenuTrigger asChild>
                          <div
                            className="inline-flex items-center justify-center h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity rounded hover:bg-slate-600/50 cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreHorizontal className="h-3 w-3" />
                          </div>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              handleEditStart(conversation.id, conversation.title);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Sửa tên
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              conversationsHook.handleDeleteConversation(conversation.id);
                            }}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Xóa
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}