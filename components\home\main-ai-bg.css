@keyframes ai-gradient {
    0% {
        filter: hue-rotate(0deg);
    }

    100% {
        filter: hue-rotate(360deg);
    }
}

.animate-ai-gradient {
    animation: ai-gradient 12s linear infinite;
}

@keyframes ai-bubble {
    0% {
        transform: translateY(0) scale(1);
        opacity: 0.4;
    }

    50% {
        transform: translateY(-20px) scale(1.08);
        opacity: 0.6;
    }

    100% {
        transform: translateY(0) scale(1);
        opacity: 0.4;
    }
}

.animate-ai-bubble {
    animation: ai-bubble 6s ease-in-out infinite;
}

@keyframes ai-float {
    0% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-18px) scale(1.08);
    }

    100% {
        transform: translateY(0);
    }
}

/* AI Icon styles */
.ai-icon {
    position: absolute;
    filter: drop-shadow(0 0 16px #60a5fa) drop-shadow(0 0 32px #818cf8);
    opacity: 0.7;
    animation: ai-float 3s ease-in-out infinite;
}

.ai-icon-pulse {
    position: absolute;
    filter: drop-shadow(0 0 16px #60a5fa) drop-shadow(0 0 32px #818cf8);
    opacity: 0.7;
    animation: ai-float 3s ease-in-out infinite, pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    top: var(--ai-top);
    left: var(--ai-left);
    animation-delay: var(--ai-delay);
}

.animate-ai-float {
    animation: ai-float 5s ease-in-out infinite;
}